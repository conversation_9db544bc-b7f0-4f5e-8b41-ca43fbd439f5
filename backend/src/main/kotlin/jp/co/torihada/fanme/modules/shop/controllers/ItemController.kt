package jp.co.torihada.fanme.modules.shop.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.CREATOR_UID_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.CREATOR_UID_TOO_MANY_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.USER_ID_TOO_MANY_LENGTH
import jp.co.torihada.fanme.modules.shop.Util.Companion.stringToInstantOnJST
import jp.co.torihada.fanme.modules.shop.controllers.requests.ItemRequest
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.usecases.item.*
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class ItemController {

    @Inject private lateinit var getItems: GetItems
    @Inject private lateinit var createItem: CreateItem
    @Inject private lateinit var updateItem: UpdateItem
    @Inject private lateinit var sortItems: SortItems
    @Inject private lateinit var getItem: GetItem

    fun getItems(
        @NotBlank(message = CREATOR_UID_IS_REQUIRED)
        @Size(max = USER_UID_MAX_LENGTH, message = CREATOR_UID_TOO_MANY_LENGTH)
        creatorUid: String,
        @Size(max = USER_UID_MAX_LENGTH, message = USER_ID_TOO_MANY_LENGTH) userId: String?,
        available: Boolean?,
        tag: String?,
        odata: OData?,
    ): List<GetItems.Item> {
        return getItems
            .execute(GetItems.Input(creatorUid, userId, available, tag, odata))
            .getOrElse {
                return emptyList()
            }
    }

    @Transactional
    fun createItem(@Valid item: ItemRequest.CreateItem): Item {
        return createItem
            .execute(
                CreateItem.Input(
                    itemType = item.itemType,
                    creatorUid = item.creatorUid,
                    name = item.name,
                    description = item.description,
                    thumbnailUri = item.thumbnailUri,
                    thumbnailFrom = item.thumbnailFrom,
                    thumbnailBlurLevel = item.thumbnailBlurLevel,
                    thumbnailWatermarkLevel = item.thumbnailWatermarkLevel,
                    price = item.price,
                    available = item.available,
                    files =
                        item.files.map {
                            CreateItem.File(
                                name = it.name,
                                objectUri = it.objectUri.substringBefore("?"),
                                thumbnailUri = it.thumbnailUri,
                                maskedThumbnailUri = it.maskedThumbnailUri,
                                price = it.price,
                                fileType = it.fileType,
                                size = it.size,
                                duration = it.duration,
                                itemThumbnailSelected = it.itemThumbnailSelected ?: false,
                                sortOrder = it.sortOrder ?: 0,
                            )
                        },
                    samples =
                        if (item.samples != null) {
                            item.samples.map {
                                CreateItem.File(
                                    name = it.name,
                                    objectUri = it.objectUri.substringBefore("?"),
                                    thumbnailUri = it.thumbnailUri,
                                    maskedThumbnailUri = null,
                                    price = it.price,
                                    fileType = it.fileType,
                                    size = it.size,
                                    duration = it.duration,
                                )
                            }
                        } else {
                            null
                        },
                    benefit =
                        if (item.benefit != null) {
                            CreateItem.BenefitParam(
                                description = item.benefit.description,
                                files =
                                    item.benefit.files.map {
                                        CreateItem.File(
                                            name = it.name,
                                            objectUri = it.objectUri.substringBefore("?"),
                                            thumbnailUri = it.thumbnailUri,
                                            maskedThumbnailUri = null,
                                            price = it.price,
                                            fileType = it.fileType,
                                            size = it.size,
                                            duration = it.duration,
                                        )
                                    },
                            )
                        } else {
                            null
                        },
                    tags = item.tags,
                    itemOption =
                        CreateItem.ItemOptionParam(
                            isSingleSales = item.itemOption.isSingleSales,
                            qtyTotal = item.itemOption.qtyTotal,
                            qtyPerUser = item.itemOption.qtyPerUser,
                            forSale =
                                CreateItem.ForSale(
                                    startAt =
                                        if (item.itemOption.forSale?.startAt == null) null
                                        else stringToInstantOnJST(item.itemOption.forSale.startAt),
                                    endAt =
                                        if (item.itemOption.forSale?.endAt == null) null
                                        else stringToInstantOnJST(item.itemOption.forSale.endAt),
                                ),
                            password = item.itemOption.password,
                            onSale =
                                if (item.itemOption.onSale != null) {
                                    CreateItem.OnSale(
                                        discountRate = item.itemOption.onSale.discountRate,
                                        startAt =
                                            if (item.itemOption.onSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.onSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.onSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.onSale.endAt),
                                    )
                                } else {
                                    null
                                },
                        ),
                )
            )
            .getOrElse { throw it }
    }

    @Transactional
    fun updateItem(@Valid item: ItemRequest.UpdateItem): Item {
        return updateItem
            .execute(
                UpdateItem.Input(
                    itemId = item.itemId,
                    creatorUid = item.creatorUid,
                    name = item.name,
                    description = item.description,
                    thumbnailUri = item.thumbnailUri,
                    thumbnailFrom = item.thumbnailFrom,
                    thumbnailBlurLevel = item.thumbnailBlurLevel,
                    thumbnailWatermarkLevel = item.thumbnailWatermarkLevel,
                    price = item.price,
                    available = item.available,
                    files =
                        item.files.map {
                            UpdateItem.File(
                                id = it.id,
                                name = it.name,
                                objectUri = it.objectUri.substringBefore("?"),
                                thumbnailUri = it.thumbnailUri,
                                maskedThumbnailUri = it.maskedThumbnailUri,
                                price = it.price,
                                fileType = it.fileType,
                                size = it.size,
                                duration = it.duration,
                                itemThumbnailSelected = it.itemThumbnailSelected ?: false,
                                sortOrder = it.sortOrder ?: 0,
                            )
                        },
                    samples =
                        if (item.samples != null) {
                            item.samples.map {
                                UpdateItem.File(
                                    id = it.id,
                                    name = it.name,
                                    objectUri = it.objectUri.substringBefore("?"),
                                    thumbnailUri = it.thumbnailUri,
                                    maskedThumbnailUri = null,
                                    price = null,
                                    fileType = it.fileType,
                                    size = it.size,
                                    duration = it.duration,
                                )
                            }
                        } else {
                            null
                        },
                    benefit =
                        if (item.benefit != null) {
                            UpdateItem.BenefitParam(
                                description = item.benefit.description,
                                files =
                                    item.benefit.files.map {
                                        UpdateItem.File(
                                            id = it.id,
                                            name = it.name,
                                            objectUri = it.objectUri.substringBefore("?"),
                                            thumbnailUri = it.thumbnailUri,
                                            maskedThumbnailUri = null,
                                            price = null,
                                            fileType = it.fileType,
                                            size = it.size,
                                            duration = it.duration,
                                        )
                                    },
                            )
                        } else {
                            null
                        },
                    tags = item.tags,
                    itemOption =
                        UpdateItem.ItemOptionParam(
                            isSingleSales = item.itemOption.isSingleSales,
                            qtyTotal = item.itemOption.qtyTotal,
                            qtyPerUser = item.itemOption.qtyPerUser,
                            forSale =
                                if (item.itemOption.forSale != null) {
                                    UpdateItem.ForSale(
                                        startAt =
                                            if (item.itemOption.forSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.forSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.forSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.forSale.endAt),
                                    )
                                } else {
                                    null
                                },
                            password = item.itemOption.password,
                            onSale =
                                if (item.itemOption.onSale != null) {
                                    UpdateItem.OnSale(
                                        discountRate = item.itemOption.onSale.discountRate,
                                        startAt =
                                            if (item.itemOption.onSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.onSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.onSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.onSale.endAt),
                                    )
                                } else {
                                    null
                                },
                        ),
                )
            )
            .getOrElse { throw it }
    }

    fun getItem(id: Long, creatorUid: String, userUid: String?): GetItem.Item {
        return getItem.execute(GetItem.Input(id, creatorUid, userUid ?: "")).getOrElse { throw it }
    }

    @Transactional
    fun sortItems(@Valid priorities: ItemRequest.SortItems): Boolean {
        return sortItems
            .execute(
                SortItems.Input(
                    priorities.userUid,
                    priorities.items.map { SortItems.SortItem(it.id, it.sortOrder) },
                )
            )
            .getOrElse { throw it }
    }
}
