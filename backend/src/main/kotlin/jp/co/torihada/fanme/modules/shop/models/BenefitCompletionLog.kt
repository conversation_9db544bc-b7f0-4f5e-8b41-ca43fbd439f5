package jp.co.torihada.fanme.modules.shop.models

import com.fasterxml.jackson.annotation.JsonFormat
import io.smallrye.common.constraint.NotNull
import jakarta.persistence.*
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const.DATE_RESPONSE_FORMAT
import jp.co.torihada.fanme.modules.shop.Const.DEFAULT_LOCAL_TIME_ZONE
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH

@PersistenceUnit(name = "benefit_completion_log")
@Entity
@Table(name = "benefit_completion_logs")
class BenefitCompletionLog : BaseModel() {

    @NotNull
    @ManyToOne
    @JoinColumn(name = "benefit_id", nullable = false, updatable = false)
    var benefit: Benefit = Benefit()

    @NotNull
    @Column(name = "user_uid", nullable = false, updatable = false, length = USER_UID_MAX_LENGTH)
    var userUid: String = ""

    @NotNull
    @Column(name = "achieved_at", nullable = false)
    @JsonFormat(pattern = DATE_RESPONSE_FORMAT, timezone = DEFAULT_LOCAL_TIME_ZONE)
    var achievedAt: Instant? = null
}
