package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class UserController : BaseController() {
    fun getUser(userUuid: String): User {
        val user = User.findByUuid(userUuid) ?: throw ResourceNotFoundException("User")
        return user
    }

    fun getUserByAccountIdentity(accountIdentity: String): User? {
        return User.findByAccountIdentity(accountIdentity)
    }

    fun getUsers(userUuids: List<String>): List<User> {
        return User.findByUuids(userUuids)
    }

    fun getUserByNotDeletedAccountIdentity(accountIdentity: String): User? {
        return User.findNotDeletedByAccountIdentity(accountIdentity)
    }
}
