package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonBackReference
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.enterprise.inject.spi.CDI
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.OneToOne
import jakarta.persistence.PersistenceUnit
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.modules.fanme.Config as FanmeConfig
import jp.co.torihada.fanme.modules.fanme.Const
import kotlin.text.get
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "content_block_details")
class ContentBlockDetail : BaseModel() {
    @Size(max = 255) @Column(name = "icon") var icon: String? = null

    val iconUrl: String?
        get() =
            icon?.let {
                val fanmeConfig = CDI.current().select(FanmeConfig::class.java).get()
                "${fanmeConfig.s3Endpoint()}/${fanmeConfig.s3BucketName()}/${Const.S3_PATH}/content_block_detail/icon/$id/$it"
            }

    @Size(max = 255) @NotNull @Column(name = "title", nullable = false) var title: String? = null

    @Column(name = "description", columnDefinition = "TEXT") var description: String? = null

    @Column(name = "app_description", columnDefinition = "TEXT") var appDescription: String? = null

    @Column(name = "url", columnDefinition = "TEXT") var url: String? = null

    @JdbcTypeCode(SqlTypes.JSON) @Column(name = "style") var style: MutableMap<String, Any>? = null

    @JsonBackReference
    @OneToOne(mappedBy = "contentBlockDetail", orphanRemoval = true)
    var contentBlockGroups: ContentBlockGroup? = null

    companion object : PanacheCompanion<ContentBlockDetail>
}
