package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonManagedReference
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "content_blocks")
class ContentBlock : BaseModel() {
    @NotNull
    @ManyToOne(optional = false)
    @JoinColumn(name = "creator_id", nullable = false)
    var user: User? = null

    @NotNull
    @Column(name = "content_block_type_id", nullable = false)
    var contentBlockTypeId: Long? = null

    @NotNull
    @Column(name = "display_order_number", nullable = false)
    var displayOrderNumber: Int? = null

    @NotNull @Column(name = "displayable", nullable = false) var displayable: Boolean? = false

    @OneToMany(mappedBy = "contentBlock", orphanRemoval = true)
    @JsonManagedReference
    var contentBlockGroups: MutableSet<ContentBlockGroup> = mutableSetOf()

    companion object : PanacheCompanion<ContentBlock> {
        fun findDisplayLast(user: User): ContentBlock? {
            return find("user = ?1 order by displayOrderNumber desc", user).firstResult()
        }

        fun findByUser(user: User): List<ContentBlock> {
            return find("user = ?1 order by displayOrderNumber asc", user).list()
        }

        fun findByUserAndId(user: User, id: Long): ContentBlock? {
            return find("user = ?1 and id = ?2", user, id).firstResult()
        }

        fun updateDisplayOrderNumbers(
            contentBlock: ContentBlock,
            displayOrderNumber: Int,
        ): ContentBlock {
            return try {
                contentBlock.apply { this.displayOrderNumber = displayOrderNumber }
                contentBlock.persistAndFlush()
                contentBlock
            } catch (e: Exception) {
                throw e
            }
        }

        fun toggleDisplayable(contentBlock: ContentBlock, displayable: Boolean): ContentBlock {
            return try {
                contentBlock.apply { this.displayable = displayable }
                contentBlock.persistAndFlush()
                contentBlock
            } catch (e: Exception) {
                throw e
            }
        }
    }
}
