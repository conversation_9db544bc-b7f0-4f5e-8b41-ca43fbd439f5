package jp.co.torihada.fanme.modules.payment.models

import com.fasterxml.jackson.annotation.JsonIgnore
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import io.quarkus.panache.common.Parameters
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import java.time.Instant
import jp.co.torihada.fanme.modules.payment.Const

@PersistenceUnit(name = "payment")
@Entity
@Table(name = "transactions")
class Transaction : BaseModel() {
    @Size(max = 10)
    @NotNull
    @Column(name = "tenant", nullable = false, length = 10)
    var tenant: String = Const.DEFAULT_TENANT

    @Size(max = 50)
    @NotNull
    @Column(name = "purchaser_user_id", nullable = false, length = 255)
    var purchaserUserId: String? = null

    @Size(max = 32)
    @Column(name = "unique_order_id", nullable = false, length = 255)
    var orderId: String? = null

    @Size(max = 50)
    @NotNull
    @Column(name = "seller_user_id", nullable = false, length = 255)
    var sellerUserId: String? = null

    @NotNull @Column(name = "ordered_at", nullable = false) var orderedAt: Instant? = null

    @NotNull @Column(name = "amount", nullable = false) var amount: Int? = null

    @NotNull @Column(name = "total_amount", nullable = false) var totalAmount: Int? = null

    @Size(max = 10)
    @NotNull
    @Column(name = "status", nullable = false, length = 255)
    var status: String? = null

    @Size(max = 7) @Column(name = "gmo_forward", length = 255) var gmoForward: String? = null

    @Size(max = 1) @Column(name = "gmo_method", length = 255) var gmoMethod: String? = null

    @Column(name = "gmo_pay_times") var gmoPayTimes: Int? = null

    @Size(max = 7) @Column(name = "gmo_approve", length = 255) var gmoApprove: String? = null

    @Size(max = 28) @Column(name = "gmo_tran_id", length = 255) var gmoTranId: String? = null

    @Size(max = 14) @Column(name = "gmo_tran_date", length = 255) var gmoTranDate: String? = null

    @Size(max = 32)
    @Column(name = "gmo_check_string", length = 255)
    var gmoCheckString: String? = null

    @Size(max = 9) @Column(name = "error_info", length = 255) var errorInfo: String? = null

    @Size(max = 20) @Column(name = "payment_type", length = 255) var paymentType: String? = null

    @OneToOne @JoinColumn(name = "payment_id") var checkout: Checkout? = null

    @OneToOne @JoinColumn(name = "tip_id") var tip: Tip? = null

    @OneToOne(mappedBy = "transaction") @JsonIgnore var sellerSale: SellerSale? = null

    @OneToOne(mappedBy = "transaction") @JsonIgnore var tenantSale: TenantSale? = null

    companion object : PanacheCompanion<Transaction> {

        fun findByIds(ids: List<Long>): List<Transaction> {
            return list("id IN (?1)", ids)
        }

        fun findLast(): Transaction? {
            return find("id = (SELECT MAX(id) FROM Transaction)").firstResult()
        }

        data class MonthlySellerAmounts(
            val tenant: String,
            val sellerUserId: String,
            val transactionAmount: Int,
            val tenantSaleAmount: Int,
            val sellerSaleAmount: Int,
        )

        fun findMonthlySellerAmounts(
            tenant: String,
            startDateTime: Instant,
            endDateTime: Instant,
            status: String? = Const.TransactionStatus.Success.value,
        ): List<MonthlySellerAmounts> {
            val query =
                """
                SELECT 
                    t.tenant AS tenant,
                    t.sellerUserId AS sellerUserId,
                    SUM(t.totalAmount) AS transactionAmount,
                    SUM(ts.amount) AS tenantSaleAmount,
                    SUM(ss.amount) AS sellerSaleAmount
                FROM Transaction t
                LEFT JOIN TenantSale ts ON t = ts.transaction
                LEFT JOIN SellerSale ss ON t = ss.transaction
                WHERE t.tenant = :tenant
                  AND t.createdAt BETWEEN :start AND :end
                  AND t.status = :status
                GROUP BY t.tenant, t.sellerUserId
            """

            return find(
                    query,
                    Parameters.with("tenant", tenant)
                        .and("start", startDateTime)
                        .and("end", endDateTime)
                        .and("status", status),
                )
                .project(Tuple::class.java)
                .list()
                .map {
                    MonthlySellerAmounts(
                        tenant = it.get("tenant") as String,
                        sellerUserId = it.get("sellerUserId") as String,
                        transactionAmount = (it.get("transactionAmount") as Number).toInt(),
                        tenantSaleAmount = (it.get("tenantSaleAmount") as Number).toInt(),
                        sellerSaleAmount = (it.get("sellerSaleAmount") as Number).toInt(),
                    )
                }
        }

        data class MonthlyTenantAmounts(
            var tenant: String? = null,
            var transactionAmount: Int? = 0,
            var tenantSaleAmount: Int? = 0,
            var sellerSaleAmount: Int? = 0,
            var gmoAmount: Int? = 0,
            var developerAmount: Int? = 0,
        )

        fun findMonthlyTenantAmounts(
            tenant: String,
            startDateTime: Instant,
            endDateTime: Instant,
            status: String? = Const.TransactionStatus.Success.value,
        ): MonthlyTenantAmounts {
            val query =
                """
                SELECT 
                    t.tenant AS tenant,
                    SUM(t.amount) AS transactionAmount,
                    SUM(ts.amount) AS tenantSaleAmount,
                    SUM(ss.amount) AS sellerSaleAmount
                FROM Transaction t
                LEFT JOIN TenantSale ts ON t = ts.transaction
                LEFT JOIN SellerSale ss ON t = ss.transaction
                WHERE t.tenant = :tenant
                  AND t.createdAt BETWEEN :start AND :end
                  AND t.status = :status
                GROUP BY t.tenant
            """

            return find(
                    query,
                    Parameters.with("tenant", tenant)
                        .and("start", startDateTime)
                        .and("end", endDateTime)
                        .and("status", status),
                )
                .project(Tuple::class.java)
                .firstResult()
                .let {
                    MonthlyTenantAmounts(
                        tenant = it?.get("tenant") as String,
                        transactionAmount = (it.get("transactionAmount") as Number).toInt(),
                        tenantSaleAmount = (it.get("tenantSaleAmount") as Number).toInt(),
                        sellerSaleAmount = (it.get("sellerSaleAmount") as Number).toInt(),
                    )
                }
        }

        fun findBySellerUserIdsAndPeriod(
            tenant: String,
            sellerUserIds: List<String>,
            startDate: Instant,
            endDate: Instant,
            status: String = Const.TransactionStatus.Success.value,
        ): List<Transaction> {
            if (sellerUserIds.isEmpty()) return emptyList()

            return find(
                    "tenant = ?1 AND sellerUserId IN ?2 AND createdAt >= ?3 AND createdAt < ?4 AND status = ?5",
                    tenant,
                    sellerUserIds,
                    startDate,
                    endDate,
                    status,
                )
                .list()
        }
    }
}
