package jp.co.torihada.fanme.modules.fanme.usecases.contentBlock

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.Const.ContentBlockType
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockGroup
import jp.co.torihada.fanme.modules.fanme.models.User

@ApplicationScoped
class CreateContentBlock {
    data class Input(val contentBlockType: ContentBlockType, val user: User)

    // TODO コンテンツAPI移植時に汎用的に作り替える
    companion object {
        private const val CONTENT_GROUP_NUMBER = 1
    }

    fun execute(params: Input): Result<ContentBlock, FanmeException> {
        val lastContent = ContentBlock.findDisplayLast(params.user)
        val nextDisplayNum = if (lastContent != null) lastContent.displayOrderNumber!! + 1 else 1

        val contentBlock =
            ContentBlock().apply {
                this.user = params.user
                this.contentBlockTypeId = params.contentBlockType.id
                this.displayOrderNumber = nextDisplayNum
                this.displayable = true
            }

        val contentBlockDetail =
            ContentBlockDetail().apply {
                this.title = ""
                this.description = ""
                this.appDescription = ""
                this.url = ""
            }

        contentBlockDetail.persist()
        contentBlock.persist()

        val contentBlockGroup =
            ContentBlockGroup().apply {
                this.contentBlock = contentBlock
                this.contentBlockDetail = contentBlockDetail
                this.contentGroupNumber = CONTENT_GROUP_NUMBER
            }
        contentBlockGroup.persist()
        contentBlock.contentBlockGroups.add(contentBlockGroup)

        return Ok(contentBlock)
    }
}
