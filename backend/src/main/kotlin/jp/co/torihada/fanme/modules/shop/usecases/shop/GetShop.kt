package jp.co.torihada.fanme.modules.shop.usecases.shop

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.lib.MaskSerializer
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.models.Shop as ShopModel
import jp.co.torihada.fanme.modules.shop.models.ShopLimitation

@ApplicationScoped
class GetShop {

    @Inject private lateinit var userController: UserController

    data class Input(val creatorUid: String)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class Shop(
        val id: Long,
        val tenant: String,
        @JsonSerialize(using = MaskSerializer::class) val creatorName: String,
        val creatorIconUri: String,
        val creatorUid: String,
        val creatorAccountIdentity: String,
        @JsonSerialize(using = MaskSerializer::class) val name: String,
        @JsonSerialize(using = MaskSerializer::class) val description: String?,
        val headerImageUri: String?,
        @JsonSerialize(using = MaskSerializer::class) val message: String?,
        var marginRate: Float,
        var isOpen: Boolean,
        var limitation: ShopLimitation,
    )

    fun execute(params: Input): Result<Shop, FanmeException> {
        val shop =
            ShopModel.findByCreatorUid(params.creatorUid)
                ?: return Err(ResourceNotFoundException("Shop"))
        val creator = userController.getUser(params.creatorUid)

        val output =
            Shop(
                id = shop.id!!,
                tenant = shop.tenant,
                creatorName = creator.name ?: "FANMEユーザー",
                creatorIconUri = creator.iconUrl,
                creatorUid = shop.creatorUid!!,
                creatorAccountIdentity = creator.accountIdentity ?: "",
                name = shop.name,
                description = shop.description,
                headerImageUri = shop.headerImageUri,
                message = shop.message,
                marginRate = shop.marginRate,
                isOpen = shop.isOpen,
                limitation =
                    shop.limitation
                        ?: ShopLimitation().apply {
                            fileCapacity = 0
                            fileQuantity = 0
                            isChekiExhibitable = false
                        },
            )

        return Ok(output)
    }
}
