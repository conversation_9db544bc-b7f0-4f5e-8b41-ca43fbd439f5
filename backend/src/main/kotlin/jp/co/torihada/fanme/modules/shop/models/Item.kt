package jp.co.torihada.fanme.modules.shop.models

import com.fasterxml.jackson.annotation.JsonIgnore
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.*
import jp.co.torihada.fanme.Util
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Const.DEFAULT_MARGIN_RATE
import jp.co.torihada.fanme.modules.shop.Const.MAX_PRICE
import jp.co.torihada.fanme.modules.shop.Const.MIN_PRICE

enum class ItemType(val value: Int) {
    DIGITAL_BUNDLE(0),
    DIGITAL_GACHA(1),
    CHEKI(2);

    companion object {
        fun fromValue(value: Int): ItemType? {
            return ItemType.entries.find { it.value == value }
        }
    }
}

@Converter(autoApply = true)
class ItemTypeConverter : AttributeConverter<ItemType, Int> {
    override fun convertToDatabaseColumn(attribute: ItemType?): Int? {
        return attribute?.value
    }

    override fun convertToEntityAttribute(dbData: Int?): ItemType? {
        return dbData?.let { ItemType.entries.find { it.value == dbData } }
    }
}

@PersistenceUnit(name = "item")
@Entity
@Table(name = "items")
class Item : BaseModel() {
    @NotNull
    @ManyToOne
    @JoinColumn(name = "shop_id", nullable = false, updatable = false)
    var shop: Shop = Shop()

    @NotBlank
    @Size(min = 1, max = 100)
    @Column(nullable = false, length = 100)
    var name: String = ""

    @Size(max = 800) @Column(length = 800) var description: String? = ""

    @NotBlank
    @Column(name = "thumbnail_uri", columnDefinition = "text", nullable = false)
    var thumbnailUri: String = ""

    @NotNull
    @Min(0)
    @Max(1)
    @Column(name = "thumbnail_from", nullable = false)
    var thumbnailFrom: Int = 0

    @NotNull
    @Min(0)
    @Max(2)
    @Column(name = "thumbnail_blur_level", nullable = false)
    var thumbnailBlurLevel: Int = 0

    @NotNull
    @Min(0)
    @Max(2)
    @Column(name = "thumbnail_watermark_level", nullable = false)
    var thumbnailWatermarkLevel: Int = 1

    @NotNull @Max(MAX_PRICE) @Min(MIN_PRICE) @Column var price: Int = MIN_PRICE.toInt()

    @NotNull
    @Column(name = "file_type", nullable = false)
    var fileType: Int = 0 // 0: none, 1: image, 2: audio, 4: video

    @NotNull @Column(nullable = false) var available: Boolean = false

    @NotNull
    @Column(name = "margin_rate", nullable = false)
    var marginRate: Float = DEFAULT_MARGIN_RATE

    @NotNull @Column(name = "sort_order", nullable = false) var sortOrder: Int = 0

    @Column(name = "item_type", nullable = true)
    @Convert(converter = ItemTypeConverter::class)
    var itemType: ItemType = ItemType.DIGITAL_BUNDLE

    @OneToMany(mappedBy = "item", orphanRemoval = true)
    @JsonIgnore
    var files: MutableSet<ItemFile> = mutableSetOf()

    fun sortedFiles(): List<ItemFile> {
        return files.sortedBy { it.sortOrder }
    }

    @OneToOne(mappedBy = "item", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var option: ItemOption? = null

    @OneToOne(mappedBy = "item", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var onSale: ItemOnSale? = null

    @OneToMany(mappedBy = "item", orphanRemoval = true)
    @JsonIgnore
    var samples: MutableSet<Sample> = mutableSetOf()

    @OneToOne(mappedBy = "item", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var benefit: Benefit? = null

    @OneToMany(mappedBy = "item", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var tags: MutableSet<ItemTag> = mutableSetOf()

    val isDigital: Boolean
        get() {
            val digitalTypes = listOf(ItemType.DIGITAL_BUNDLE, ItemType.DIGITAL_GACHA)
            return digitalTypes.contains(ItemType.fromValue(itemType.value))
        }

    @OneToOne(mappedBy = "item", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var gachaItem: GachaItem? = null

    @OneToMany(mappedBy = "item", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var userBadge: MutableSet<UserBadge> = mutableSetOf()

    @OneToMany(mappedBy = "item", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var orderItem: MutableSet<OrderItem> = mutableSetOf()

    companion object : PanacheCompanion<Item> {

        fun findByShopId(shopId: Long, available: Boolean, top: Int?, skip: Int?): List<Item> {
            val orderByQuery = " ORDER BY sortOrder, createdAt DESC"
            val query = if (available) "shop.id = ?1 and available = true" else "shop.id = ?1"
            val find = find(query + orderByQuery, shopId)
            return Util.getEntityListWithPagination(find, top, skip) as List<Item>
        }

        fun findByIds(ids: List<Long>): List<Item> {
            return list("id in ?1", ids)
        }

        fun create(
            shopId: Long,
            name: String,
            description: String?,
            thumbnailUri: String,
            thumbnailFrom: Int,
            thumbnailBlurLevel: Int,
            thumbnailWatermarkLevel: Int,
            price: Int,
            fileType: Int,
            available: Boolean,
            itemType: ItemType = ItemType.DIGITAL_BUNDLE,
            marginRate: Float = DEFAULT_MARGIN_RATE,
        ): Item {
            val item = Item()
            item.shop = Shop.findById(shopId)!!
            item.name = name
            item.description = description
            item.thumbnailUri = thumbnailUri
            item.thumbnailFrom = thumbnailFrom
            item.thumbnailBlurLevel = thumbnailBlurLevel
            item.thumbnailWatermarkLevel = thumbnailWatermarkLevel
            item.price = price
            item.fileType = fileType
            item.available = available
            item.marginRate = marginRate
            item.itemType = itemType
            item.persist()
            return item
        }

        fun update(
            id: Long,
            name: String,
            description: String?,
            thumbnailUri: String,
            thumbnailFrom: Int,
            thumbnailBlurLevel: Int,
            thumbnailWatermarkLevel: Int,
            price: Int? = null,
            fileType: Int,
            available: Boolean,
        ): Item {
            val item = findById(id) ?: throw ResourceNotFoundException("Item")
            item.name = name
            item.description = description
            item.thumbnailUri = thumbnailUri
            item.thumbnailFrom = thumbnailFrom
            item.thumbnailBlurLevel = thumbnailBlurLevel
            item.thumbnailWatermarkLevel = thumbnailWatermarkLevel
            if (price != null) {
                item.price = price
            }
            item.fileType = fileType
            item.available = available
            item.persist()
            return item
        }
    }
}
