package jp.co.torihada.fanme.modules.shop.usecases.order

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.shop.Const
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemType

@ApplicationScoped
class GetDeliveryFee {
    @Inject lateinit var config: Config

    class Input(val items: List<Item>)

    class Output(val deliveryFee: Int?)

    /*
     * TODO リリース後トグル削除
     * チェキの配送手数料を固定で設定
     * 物販実装時に手数料取得部分の修正必須
     */
    fun execute(params: Input): Output {
        val deliveryFee =
            if (config.featureToggleCheki() && params.items.any { it.itemType == ItemType.CHEKI })
                Const.CHEKI_DELIVERY_FEE
            else null

        return Output(deliveryFee)
    }
}
