package jp.co.torihada.fanme.modules.shop.usecases.cartItem

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.models.Cart
import jp.co.torihada.fanme.modules.shop.models.CartItem
import jp.co.torihada.fanme.modules.shop.models.Order

@ApplicationScoped
class DeleteCartItems {
    fun execute(checkoutId: Long) {
        val order = Order.findByCheckoutId(checkoutId) ?: throw ResourceNotFoundException("Order")
        val cart =
            Cart.findByUserUidAndShopId(order.purchaserUid, order.shop.id!!)
                ?: throw ResourceNotFoundException("Cart")
        val cartItems = cart.items.toList()
        val cartItemIds = cartItems.map { it.id!! }
        CartItem.deleteItems(cartItemIds)
    }
}
