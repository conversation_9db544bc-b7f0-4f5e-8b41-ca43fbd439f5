package jp.co.torihada.fanme.modules.payment.usecases.transaction

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.payment.utils.YearMonth

@ApplicationScoped
class GetTransactions {

    data class Input(
        val transactionIds: List<Long>? = null,
        val sellerUserIds: List<String>? = null,
        val yearMonth: String? = null,
    )

    fun execute(params: Input): Result<List<Transaction>, FanmeException> {
        return when {
            params.transactionIds != null -> {
                Ok(Transaction.findByIds(params.transactionIds))
            }
            params.sellerUserIds != null && params.yearMonth != null -> {
                val (startDate, endDate) = YearMonth.toMonthPeriod(params.yearMonth)
                val transactions =
                    Transaction.findBySellerUserIdsAndPeriod(
                        Const.DEFAULT_TENANT,
                        params.sellerUserIds,
                        startDate,
                        endDate,
                    )
                Ok(transactions)
            }
            else -> {
                Ok(emptyList())
            }
        }
    }
}
