package jp.co.torihada.fanme.modules.shop.usecases.order

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.shop.models.Order
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetOrders {

    data class Input(val userId: String, val odata: OData)

    data class Output(val orders: List<OrderDetail>, val count: Int)

    data class OrderDetail(
        val purchaserUid: String,
        val purchasedItems: List<PurchasedItem>,
        val price: Int,
        val createdAt: String,
        val updatedAt: String,
    )

    data class PurchasedItem(
        val itemId: Long,
        val name: String,
        val thumbnailUri: String,
        val price: Int,
        val fileType: String,
    )

    fun execute(params: Input): Result<List<Order>, FanmeException> {
        return Ok(Order.findByPurchaserUid(params.userId, params.odata.top, params.odata.skip))
    }
}
