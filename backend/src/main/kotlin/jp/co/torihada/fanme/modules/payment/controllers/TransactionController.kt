package jp.co.torihada.fanme.modules.payment.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.payment.usecases.transaction.GetTransaction
import jp.co.torihada.fanme.modules.payment.usecases.transaction.GetTransactions

@ApplicationScoped
class TransactionController {

    @Inject private lateinit var getTransaction: GetTransaction

    @Inject private lateinit var getTransactions: GetTransactions

    fun getTransaction(transactionId: Long): Transaction {
        return getTransaction.execute(GetTransaction.Input(transactionId)).getOrThrow()
    }

    fun getTransactions(transactionIds: List<Long>): List<Transaction> {
        return getTransactions
            .execute(GetTransactions.Input(transactionIds = transactionIds))
            .getOrThrow()
    }

    fun getSellerTransactions(sellerUserIds: List<String>, yearMonth: String): List<Transaction> {
        return getTransactions
            .execute(GetTransactions.Input(sellerUserIds = sellerUserIds, yearMonth = yearMonth))
            .getOrThrow()
    }
}
