package jp.co.torihada.fanme

object Const {

    object Order {
        const val NUMBER_PREFIX = "O"
        const val NUMBER_LENGTH = 10
    }

    const val PAYPAY_CALLBACK_TOKEN_KEY = "paypay_callback_token"

    enum class ContentBlockType(val id: Long) {
        ONE_BLOCK(1L),
        TWO_BLOCKS(2L),
        THREE_BLOCKS(3L),
        FOUR_BLOCKS(4L),
        IMAGE_LIST(5L),
        TEXT_LIST(6L),
        HEADLINE(7L),
        SHORTS(8L),
        MARGIN(9L);

        companion object {
            fun fromId(id: Long): ContentBlockType? {
                return ContentBlockType.entries.find { it.id == id }
            }
        }
    }

    enum class CheckoutStatus(val value: String, val displayName: String) {
        UNPROCESSED("UNPROCESSED", "未処理"),
        REQSUCCESS("REQSUCCESS", "支払い申込中"),
        PAYSUCCESS("PAYSUCCESS", "購入済み"),
        EXPIRED("EXPIRED", "支払い期限切れ"),
        CANCEL("CANCEL", "キャンセル済み"),
        PAYFAILED("PAYFAILED", "購入失敗");

        companion object {
            private val map = CheckoutStatus.entries.associateBy(CheckoutStatus::value)

            fun fromValue(value: String) = map[value]
        }
    }

    object RestrictedAccountType {
        const val STOP_SELLER_TRANSFER = 1 // 出金停止
        const val HIDE_SALES =
            2 // 売上金非表示(https://www.notion.so/torihada/4-1bdbeeb37cc2806b9ccdcb1053bfe360)
    }
}
