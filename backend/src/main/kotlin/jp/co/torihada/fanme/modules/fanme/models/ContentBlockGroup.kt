package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonBackReference
import com.fasterxml.jackson.annotation.JsonManagedReference
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull

@PersistenceUnit(name = "fanme")
@Entity
@Table(name = "content_block_groups")
class ContentBlockGroup : BaseModel() {
    @NotNull
    @ManyToOne(optional = false)
    @JsonBackReference
    @JoinColumn(name = "content_block_id", nullable = false)
    var contentBlock: ContentBlock? = null

    @NotNull
    @OneToOne(optional = false)
    @JsonManagedReference
    @JoinColumn(name = "content_block_detail_id", nullable = false)
    var contentBlockDetail: ContentBlockDetail? = null

    @NotNull
    @Column(name = "content_group_number", nullable = false)
    var contentGroupNumber: Int? = null

    companion object : PanacheCompanion<ContentBlockGroup> {

        fun findByContentBlock(contentBlock: ContentBlock): List<ContentBlockGroup> {
            return find("contentBlock = ?1 order by contentGroupNumber asc", contentBlock).list()
        }

        fun findByContentBlockDetailId(contentBlockDetailId: Long): ContentBlockGroup? {
            return find(
                    "contentBlockDetail.id = ?1 order by contentGroupNumber asc",
                    contentBlockDetailId,
                )
                .firstResult()
        }

        fun updateContentGroupNumber(
            contentBlockGroup: ContentBlockGroup,
            contentGroupNumber: Int,
        ): ContentBlockGroup {
            return try {
                contentBlockGroup.apply { this.contentGroupNumber = contentGroupNumber }
                contentBlockGroup.persistAndFlush()
                contentBlockGroup
            } catch (e: Exception) {
                throw e
            }
        }
    }
}
