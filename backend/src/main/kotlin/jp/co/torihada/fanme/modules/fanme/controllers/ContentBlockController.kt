package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import com.github.michaelbull.result.getOrElse
import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.Const.ContentBlockType
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.Const.CONTENT_DESCRIPTION_MAX_LENGTH
import jp.co.torihada.fanme.modules.fanme.Const.CONTENT_TITLE_MAX_LENGTH
import jp.co.torihada.fanme.modules.fanme.Const.USER_UID_MAX_LENGTH
import jp.co.torihada.fanme.modules.fanme.models.ContentBlock
import jp.co.torihada.fanme.modules.fanme.models.ContentBlockGroup
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.CreateContentBlock
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.GetContentBlocks
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.MoveContentBlock
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.MoveContentBlockDetail
import jp.co.torihada.fanme.modules.fanme.usecases.contentBlock.ToggleContentBlockDisplayable
import jp.co.torihada.fanme.modules.fanme.usecases.user.GetCurrentUser
import jp.co.torihada.fanme.modules.fanme.usecases.user.GetUser
import org.hibernate.validator.constraints.URL

@ApplicationScoped
class ContentBlockController : BaseController() {

    @Inject private lateinit var getUser: GetUser

    @Inject private lateinit var getCurrentUser: GetCurrentUser
    @Inject private lateinit var getUserContentBlocks: GetContentBlocks
    @Inject private lateinit var createContentBlock: CreateContentBlock
    @Inject private lateinit var moveContentBlock: MoveContentBlock
    @Inject private lateinit var moveContentBlockDetail: MoveContentBlockDetail
    @Inject private lateinit var toggleContentBlockDisplayable: ToggleContentBlockDisplayable

    fun getUserContentBlocks(
        @NotBlank creatorAccountIdentity: String
    ): List<GetContentBlocks.ContentBlock> {
        val user =
            getUser.execute(GetUser.Input(accountIdentity = creatorAccountIdentity)).getOrElse {
                throw it
            }
        return getUserContentBlocks.execute(GetContentBlocks.Input(user = user)).getOrElse {
            throw it
        }
    }

    fun getCurrentUserContentBlocks(
        @NotBlank userUid: String
    ): List<GetContentBlocks.ContentBlock> {
        val user =
            getCurrentUser.execute(GetCurrentUser.Input(userUid = userUid)).getOrElse { throw it }
        return getUserContentBlocks.execute(GetContentBlocks.Input(user = user)).getOrElse {
            throw it
        }
    }

    data class CreateContentBlockWithDetailRequest(
        @NotBlank @Size(max = USER_UID_MAX_LENGTH) val creatorUid: String,
        val contentBlockType: ContentBlockType,
        @NotBlank @Size(max = CONTENT_TITLE_MAX_LENGTH) val title: String,
        @Size(max = CONTENT_DESCRIPTION_MAX_LENGTH) val description: String? = null,
        @Size(max = CONTENT_DESCRIPTION_MAX_LENGTH) val appDescription: String? = null,
        @URL val url: String,
        @URL val iconUrl: String? = null,
    )

    @Transactional(rollbackOn = [Exception::class])
    fun createContentBlockWithDetail(
        @Valid request: CreateContentBlockWithDetailRequest
    ): ContentBlock {
        val user =
            getCurrentUser.execute(GetCurrentUser.Input(request.creatorUid)).getOrElse { throw it }

        val contentBlock =
            createContentBlock
                .execute(
                    CreateContentBlock.Input(
                        contentBlockType = request.contentBlockType,
                        user = user,
                    )
                )
                .getOrThrow()

        contentBlock.contentBlockGroups.first().contentBlockDetail!!.apply {
            title = request.title
            description = request.description ?: ""
            appDescription = request.appDescription ?: ""
            url = request.url
            icon = request.iconUrl
        }

        return contentBlock
    }

    fun moveContentBlock(
        userUid: String,
        displayOrderNum: Int?,
        upDown: String?,
        contentBlockIds: List<Long>?,
    ): List<ContentBlock> {
        val user =
            getCurrentUser.execute(GetCurrentUser.Input(userUid = userUid)).getOrElse { throw it }

        // displayOrderNumとupDownの組み合わせでの並び替え(現行のfanmeの渡し方)
        // Listで渡す形に統一したいのでuseCase化しない
        if (displayOrderNum != null && upDown != null) {
            val contentBlocks = user.contentBlocks.sortedBy { it.displayOrderNumber }
            val idList = contentBlocks.map { it.id!! }.toMutableList()

            val targetBlock =
                contentBlocks.find { it.displayOrderNumber == displayOrderNum }
                    ?: throw ResourceNotFoundException("ContentBlock")

            val swapTargetBlock =
                when (upDown) {
                    "up" -> contentBlocks.find { it.displayOrderNumber == displayOrderNum + 1 }
                    "down" -> contentBlocks.find { it.displayOrderNumber == displayOrderNum - 1 }
                    else -> throw IllegalArgumentException("Invalid upDown value: $upDown")
                } ?: throw ResourceNotFoundException("ContentBlock")

            val targetIndex = idList.indexOf(targetBlock.id)
            val swapIndex = idList.indexOf(swapTargetBlock.id)

            if (targetIndex >= 0 && swapIndex >= 0) {
                idList[targetIndex] = swapTargetBlock.id!!
                idList[swapIndex] = targetBlock.id!!
            }

            return moveContentBlock
                .execute(MoveContentBlock.Input(user = user, contentBlockIds = idList))
                .getOrElse { throw it }
        }

        // 以下のように変更後の並び順をListで渡す形に統一する
        if (contentBlockIds != null) {
            return moveContentBlock
                .execute(MoveContentBlock.Input(user = user, contentBlockIds = contentBlockIds))
                .getOrElse { throw it }
        }
        throw IllegalArgumentException(
            "Either displayOrderNum and upDown or contentBlockIds must be provided"
        )
    }

    fun moveContentBlockDetail(
        fromContentBlockDetailId: Long?,
        toContentBlockDetailId: Long?,
        contentBlocksIds: List<Long>?,
    ): ContentBlock {
        // fromContentBlockDetailIdとtoContentBlockDetailIdを受け取る場合(現行のfanmeの渡し方)
        // Listで渡す形に統一したいのでuseCase化しない
        if (fromContentBlockDetailId != null && toContentBlockDetailId != null) {
            val targetGroup =
                ContentBlockGroup.findByContentBlockDetailId(fromContentBlockDetailId)
                    ?: throw ResourceNotFoundException("ContentBlockGroup")
            val targetContentBlock =
                targetGroup.contentBlock ?: throw ResourceNotFoundException("ContentBlock")
            val detailIds =
                targetContentBlock.contentBlockGroups
                    .map { it.contentBlockDetail!!.id }
                    .toMutableList()

            // fromContentBlockDetailIdとtoContentBlockDetailIdの順番を入れ替える
            val fromIndex = detailIds.indexOf(fromContentBlockDetailId)
            val toIndex = detailIds.indexOf(toContentBlockDetailId)

            if (fromIndex >= 0 && toIndex >= 0) {
                detailIds[fromIndex] = toContentBlockDetailId
                detailIds[toIndex] = fromContentBlockDetailId
            }

            return moveContentBlockDetail
                .execute(
                    input =
                        MoveContentBlockDetail.Input(
                            contentBlockDetailIds = listOf(toContentBlockDetailId)
                        )
                )
                .getOrElse { throw it }
        }

        // 以下のように変更後の並び順をListで渡す形に統一する
        if (contentBlocksIds != null) {
            return moveContentBlockDetail
                .execute(
                    input = MoveContentBlockDetail.Input(contentBlockDetailIds = contentBlocksIds)
                )
                .getOrElse { throw it }
        }
        throw IllegalArgumentException(
            "Either fromContentBlockDetailId and toContentBlockDetailId or contentBlocksIds must be provided"
        )
    }

    fun toggleContentBlockDisplayable(
        userUid: String,
        displayOrderNum: Int?,
        contentBlockId: Long?,
        displayable: Boolean?,
    ): ContentBlock {
        val user =
            getCurrentUser.execute(GetCurrentUser.Input(userUid = userUid)).getOrElse { throw it }
        // displayOrderNumを受け取る場合(現行のfanmeの渡し方)
        // contentBlockIdで渡す形に統一したいのでuseCase化しない
        if (displayOrderNum != null) {
            val contentBlock =
                user.contentBlocks.find { it.displayOrderNumber == displayOrderNum }
                    ?: throw ResourceNotFoundException("ContentBlock")
            val displayable = contentBlock.displayable!!
            val updatedContentBlock =
                toggleContentBlockDisplayable
                    .execute(
                        ToggleContentBlockDisplayable.Input(
                            contentBlockId = contentBlock.id!!,
                            displayable = !displayable,
                        )
                    )
                    .getOrElse { throw it }
            return updatedContentBlock
        }

        if (contentBlockId != null && displayable != null) {
            val updatedContentBlock =
                toggleContentBlockDisplayable
                    .execute(
                        ToggleContentBlockDisplayable.Input(
                            contentBlockId = contentBlockId,
                            displayable = displayable,
                        )
                    )
                    .getOrElse { throw it }
            return updatedContentBlock
        }
        throw IllegalArgumentException(
            "Either displayOrderNum or contentBlockId and displayable must be provided"
        )
    }
}
