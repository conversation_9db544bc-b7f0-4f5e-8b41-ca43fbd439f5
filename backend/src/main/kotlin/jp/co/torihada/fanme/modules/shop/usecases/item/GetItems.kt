package jp.co.torihada.fanme.modules.shop.usecases.item

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.lib.MaskSerializer
import jp.co.torihada.fanme.modules.shop.Const
import jp.co.torihada.fanme.modules.shop.Util.Companion.getCurrentPrice
import jp.co.torihada.fanme.modules.shop.models.*
import jp.co.torihada.fanme.modules.shop.models.Item as ItemModel
import jp.co.torihada.fanme.odata.OData
import org.jboss.logging.Logger

@ApplicationScoped
class GetItems {
    @Inject lateinit var logger: Logger

    data class Input(
        val ownerUid: String,
        val userUid: String?,
        val available: Boolean?,
        val tag: String?,
        val odata: OData?,
        val applyMasking: Boolean = true,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class Item(
        val id: Long,
        @JsonSerialize(using = MaskSerializer::class) val name: String,
        @JsonSerialize(using = MaskSerializer::class) val description: String?,
        val thumbnailUri: String,
        val price: Int,
        val currentPrice: Int,
        val minPrice: Int,
        val fileType: String,
        val itemType: Int = ItemType.DIGITAL_BUNDLE.value,
        val fileQuantities: List<FileQuantity>?,
        val available: Boolean,
        val hasBenefit: Boolean,
        val tags: List<String>?,
        val itemOption: OptionData,
        val isPurchased: Boolean = false,
        val isCheckout: Boolean = false,
        val isCompleted: Boolean = false,
        val isNew: Boolean = false,
        val collectedUniqueItemsCount: Int = 0,
        val createdAt: String,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class OptionData(
        val isSingleSales: Boolean,
        val qtyTotal: Int?,
        val remainingAmount: Int?,
        val forSale: ForSaleData?,
        val password: String?,
        val onSale: OnSaleData?,
    )

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class ForSaleData(val startAt: String?, val endAt: String?)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class OnSaleData(val discountRate: Float, val startAt: String?, val endAt: String?)

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class FileQuantity(val fileType: String, val quantity: Int)

    fun execute(params: Input): Result<List<Item>, FanmeException> {
        val shop =
            Shop.findByCreatorUid(params.ownerUid) ?: return Err(ResourceNotFoundException("Shop"))
        // 自分の商品は全て取得
        if (params.ownerUid == (params.userUid ?: "")) {
            // 有効な商品のみ取得
            if (params.available == true) {
                try {
                    val output = getAvailableItems(params, shop.id!!)
                    return Ok(output)
                } catch (e: FanmeException) {
                    return Err(e)
                }
            }
            // 商品を全て取得
            val items =
                ItemModel.findByShopId(shop.id!!, false, params.odata?.top, params.odata?.skip)
            val tags = items.map { item: ItemModel -> item.tags }.flatten()
            val filteredItems =
                if (params.tag != null) {
                    items.filter { item ->
                        tags.find { it.item.id == item.id }?.tag?.tag == params.tag
                    }
                } else {
                    items
                }
            try {
                val output = createOutput(filteredItems, params.userUid)
                return Ok(output)
            } catch (e: FanmeException) {
                return Err(e)
            }
        }
        // 他人の商品は有効な商品のみ取得
        try {
            val output = getAvailableItems(params, shop.id!!)
            return Ok(output)
        } catch (e: FanmeException) {
            return Err(e)
        }
    }

    private fun getAvailableItems(params: Input, shopId: Long): List<Item> {
        val items = ItemModel.findByShopId(shopId, true, params.odata?.top, params.odata?.skip)
        val options = ItemOption.findByItemIds(items.map { it.id!! })
        val availableItems = getForSaleItems(items, options)
        val tags = ItemTag.findByItemIds(availableItems.map { it.id!! })
        val filteredItems = getFilteredItems(availableItems, tags, params.tag)
        return createOutput(filteredItems, params.userUid)
    }

    private fun getForSaleItems(
        items: List<ItemModel>,
        options: List<ItemOption>,
    ): List<ItemModel> {
        val now = Instant.now()
        return items.filter { item ->
            val option = options.find { it.item.id == item.id }!!
            option.forSaleStartAt?.isBefore(now) ?: true &&
                option.forSaleEndAt?.isAfter(now) ?: true
        }
    }

    private fun getFilteredItems(
        items: List<ItemModel>,
        tags: List<ItemTag>,
        tag: String?,
    ): List<ItemModel> {
        return if (tag != null) {
            items.filter { item -> tags.find { it.item.id == item.id }?.tag?.tag == tag }
        } else {
            items
        }
    }

    private fun createOutput(items: List<ItemModel>, userUid: String?): List<Item> {
        val outputs =
            items.map { item ->
                val option = item.option ?: throw ResourceNotFoundException("ItemOption")
                val benefit = item.benefit
                val onSale = item.onSale
                val tags = item.tags.map { it.tag.tag }
                val files = item.files.takeIf { it.isNotEmpty() }?.let { item.sortedFiles() }
                val minPrice = files?.minOfOrNull { it.price ?: item.price } ?: item.price
                val isPurchased =
                    if (userUid != null) PurchasedItem.isPurchased(item.id!!, userUid, null)
                    else false
                val isCheckout =
                    if (userUid != null) PurchasedItem.isCheckout(item.id!!, userUid, null)
                    else false
                val remainingAmount =
                    if (option.qtyTotal != null)
                        option.qtyTotal!! - PurchasedItem.countPurchasedItem(item.id!!)
                    else null
                // gacha_received_filesを確認し、取得したユニークなファイルの数を取得
                val collectedUniqueItemsCount =
                    if (userUid != null)
                        GachaReceivedFile.findByPurchaserUidAndItemId(userUid, item.id!!)
                            .map { it?.itemFile?.id }
                            .distinct()
                            .size
                    else 0

                val isCompleted =
                    if (userUid != null) {
                        val gachaItem = item.gachaItem
                        if (gachaItem != null) {
                            gachaItem.item.files.distinct().size == collectedUniqueItemsCount
                        } else false
                    } else false

                // createdAtから7日以内であればisNewをtrueにする
                val isNew =
                    if (userUid != null) {
                        val createdAt =
                            LocalDateTime.ofInstant(
                                item.createdAt,
                                ZoneId.of(Const.DEFAULT_LOCAL_TIME_ZONE),
                            )
                        val now = LocalDateTime.now(ZoneId.of(Const.DEFAULT_LOCAL_TIME_ZONE))
                        val diff = now.minusDays(7)
                        createdAt.isAfter(diff)
                    } else false

                Item(
                    id = item.id!!,
                    name = item.name,
                    description = item.description,
                    thumbnailUri = item.thumbnailUri,
                    price = item.price,
                    currentPrice = getCurrentPrice(item),
                    minPrice = minPrice,
                    fileType = item.fileType.toString(2).padStart(3, '0').reversed(),
                    itemType = item.itemType.value,
                    fileQuantities =
                        files
                            ?.groupBy { it.fileType }
                            ?.map { fileType -> FileQuantity(fileType.key, fileType.value.size) },
                    available = item.available,
                    hasBenefit = benefit != null,
                    tags = tags,
                    itemOption =
                        OptionData(
                            isSingleSales = option.isSingleSales,
                            qtyTotal = option.qtyTotal,
                            remainingAmount = remainingAmount,
                            forSale =
                                ForSaleData(
                                    option.forSaleStartAt?.toString(),
                                    option.forSaleEndAt?.toString(),
                                ),
                            password = option.password,
                            onSale =
                                onSale?.let {
                                    OnSaleData(
                                        it.discountRate,
                                        it.startAt.toString(),
                                        it.endAt.toString(),
                                    )
                                },
                        ),
                    isPurchased = isPurchased,
                    isCheckout = isCheckout,
                    isCompleted = isCompleted,
                    isNew = isNew,
                    collectedUniqueItemsCount = collectedUniqueItemsCount,
                    createdAt =
                        LocalDateTime.ofInstant(
                                item.createdAt,
                                ZoneId.of(Const.DEFAULT_LOCAL_TIME_ZONE),
                            )
                            .toString(),
                )
            }

        return outputs
    }
}
