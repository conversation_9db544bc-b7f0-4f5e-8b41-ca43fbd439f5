#
# Common
#
config.tenant=fanme
config.env-kind=dev
config.host-url=http://host.docker.internal:27000
config.auth-api-name=fanme.auth.api
config.auth-api-pass=dfgsdjierre11024TDHij)jpjgadfa{-fsefdsgasdfewfkHklfeowabmcfgsStfsf
config.shop-api-name=fanme.shop.api
config.shop-api-pass=huieRE10248{-jdsJDFLewefjsgadfasGfdhjklfjdsfjkl
config.payment-api-name=fanme.payment.api
config.payment-api-pass=wInomelAdSyIxlulhCeft-{eLES00O23ZWyuuk5MXRUsNRjbgsAX
config.salesforce-client-id=dummy
config.salesforce-client-secret=dummy
#
# fanme
#
config.fanme.fanme-api-server-url=http://host.docker.internal:3000
config.fanme.auth-server-url=http://host.docker.internal:22000
config.fanme.payment-url=http://host.docker.internal:23000
config.fanme.fanme-auth-client-id=test_id_2
config.fanme.fanme-auth-client-secret=test_secret_2
config.fanme.fanme-frontend-url=http://host.docker.internal:11000
config.fanme.s3-bucket-name=fanme-dev-api
config.fanme.s3-endpoint=${quarkus.s3.endpoint-override}
config.fanme.kawa-auth-token=prkb7w3wSclVi6/xmZMh2y1pEjN3QbOgUv/BHaTa6slA9iMi
#
# payment
#
# GMO
config.payment.gmo-site-id=tsite00046822
config.payment.gmo-site-pass=m3xvyp6g
config.payment.gmo-shop-id=tshop00054898
config.payment.gmo-shop-pass=ch3q8wfn
config.payment.gmo-product-shop-id=tshop00066824
config.payment.gmo-product-shop-pass=2cauwncy
config.payment.gmo-transfer-shop-id=rshop00000567
config.payment.gmo-transfer-shop-pass=t8k8x3cy
config.payment.gmo-webhook-allowed-remote-ip=***************
#
# shop
#
config.shop.shop-front-url=http://host.docker.internal:27002/shop
config.shop.shop-payment-url=http://host.docker.internal:27000
config.shop.s3-endpoint=${quarkus.s3.endpoint-override}
config.shop.s3-bucket-name=fanme-dev-shop
config.shop.s3-bucket-name-for-smapo=smapo-shared-files
config.shop.smapo-gcp-bucket-name=dummy
#
# DB
#
# fanme
quarkus.datasource.fanme.db-kind=mysql
quarkus.datasource.fanme.username=root
quarkus.datasource.fanme.password=password
quarkus.datasource.fanme.jdbc.url=*************************************************
quarkus.datasource.fanme.jdbc.max-size=3
quarkus.hibernate-orm.fanme.datasource=fanme
quarkus.hibernate-orm.fanme.packages=jp.co.torihada.fanme.modules.fanme.models,jp.co.torihada.fanme.modules.console.models
# payment
quarkus.datasource.payment.db-kind=mysql
quarkus.datasource.payment.username=root
quarkus.datasource.payment.password=pass
quarkus.datasource.payment.jdbc.url=***********************************************
quarkus.datasource.payment.jdbc.max-size=3
quarkus.hibernate-orm.payment.datasource=payment
quarkus.hibernate-orm.payment.packages=jp.co.torihada.fanme.modules.payment.models
# shop
quarkus.datasource.shop.db-kind=mysql
quarkus.datasource.shop.username=root
quarkus.datasource.shop.password=pass
quarkus.datasource.shop.jdbc.url=********************************************
quarkus.datasource.shop.jdbc.max-size=3
quarkus.hibernate-orm.shop.datasource=shop
quarkus.hibernate-orm.shop.packages=jp.co.torihada.fanme.modules.shop.models
# flyway
quarkus.flyway.fanme.migrate-at-start=true
quarkus.flyway.payment.migrate-at-start=true
quarkus.flyway.shop.migrate-at-start=true
quarkus.flyway.out-of-order=true
quarkus.flyway.fanme.locations=db/migration/fanme
quarkus.flyway.payment.locations=db/migration/payment
quarkus.flyway.shop.locations=db/migration/shop
#quarkus.flyway.fame.locations=db/migration/console
#
# JWT
#
mp.jwt.verify.publickey.location=http://host.docker.internal:22000/oauth2/certs
mp.jwt.verify.issuer=http://host.docker.internal:22000
#
# Log
#
quarkus.log.level=INFO
quarkus.http.access-log.enabled=true
quarkus.log.console.json=false
quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5p %c{1} - %s%e%n
quarkus.http.access-log.pattern=%s %m - %R %Dms
quarkus.http.access-log.exclude-pattern=^/hc$
#
# Client
#
quarkus.rest-client.auth.uri=${config.fanme.auth-server-url}
quarkus.rest-client.shop.uri=http://host.docker.internal:27000
quarkus.rest-client.payment.uri=http://host.docker.internal:23000
quarkus.rest-client.gmo-mul-pay.uri=https://pt01.mul-pay.jp
quarkus.rest-client.gmo-remittance.uri=https://test-remittance.gmopg.jp
quarkus.rest-client.gmo-transfer.uri=https://test-remittance.gmopg.jp
quarkus.rest-client.salesforce.uri=https://torihada.my.salesforce.com
quarkus.rest-client.suggest-address.uri=https://kawa.plt.ttkpf.com
quarkus.rest-client.gcp-storage-api.uri=https://storage.googleapis.com
#
# S3
#
quarkus.s3.path-style-access=true
quarkus.s3.endpoint-override=http://host.docker.internal:23003
quarkus.s3.sync-client.type=url
quarkus.s3.aws.region=ap-northeast-1
quarkus.s3.aws.credentials.type=static
quarkus.s3.aws.credentials.static-provider.access-key-id=test_key
quarkus.s3.aws.credentials.static-provider.secret-access-key=test_key
#
# Mail
#
quarkus.mailer.from=<EMAIL> (FANME)
quarkus.mailer.host=host.docker.internal
quarkus.mailer.port=22002
quarkus.mailer.username=test
quarkus.mailer.password=test
#
# CORS
#
quarkus.http.cors=true
quarkus.http.cors.origins=*
quarkus.http.cors.methods=GET,PUT,POST,DELETE,OPTIONS
quarkus.http.cors.headers=accept,accept-type,authorization,content-type
quarkus.http.cors.access-control-max-age=600
quarkus.http.cors.access-control-allow-credentials=true
#
# OpenAPI
#
mp.openapi.extensions.smallrye.operationIdStrategy=METHOD
mp.openapi.scan.exclude.classes=jp.co.torihada.fanme.endpoints.payment.CallbackEndpoints
#
# Feature Toggle
#
config.feature-toggle-cheki=false
