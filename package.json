{"name": "fanme-shop", "version": "1.0.0", "scripts": {"prepare": "husky", "start": "cd ./frontend/docker && docker compose -p fanme-shop-front up -d && cd ./../../backend/docker && docker compose -p fanme-backend up -d", "end": "cd ./frontend/docker && docker compose -p fanme-shop-front down && cd ./../../backend/docker && docker compose -p fanme-backend down", "openapi": "curl -X GET http://host.docker.internal:27000/q/openapi -o ./backend/META-INF/openapi.yaml", "orval": "yarn clean:openapi && yarn openapi && cd frontend && yarn orval-generate", "clean:openapi": "rm -rf ./frontend/src/lib/server-api && rm -rf ./frontend/src/lib/client-api", "lint-fix:backend": "cd ./backend && ./gradlew ktfmtFormat", "lint-fix:frontend": "cd ./frontend && yarn lint-fix", "lint-fix": "npx concurrently --group --names \"Backend,Frontend\" \"yarn lint-fix:backend\" \"yarn lint-fix:frontend\""}, "_comment": {"openapi": "openapi の更新コマンド"}, "private": true, "devDependencies": {"concurrently": "^9.1.2", "husky": "^9.1.7"}}