import { CropperProps } from 'react-easy-crop';
import { ReactNode } from 'react';
import { create } from 'zustand';

type CropProps = Pick<CropperProps, 'image' | 'aspect' | 'onMediaLoaded' | 'cropSize' | 'minZoom'> & {
  onCropComplete: (img: string) => void;
  maskText?: ReactNode;
  showAspectToggle?: boolean;
  borderColor?: string;
};
interface ICropStore {
  isCropOpen: boolean;
  onCropOpen: () => void;
  onCropClose: () => void;
  cropProps: CropProps;
  setCropperProps: (props: CropProps) => void;
}

export const useThumbnailStore = create<ICropStore>()((set) => ({
  isCropOpen: false,
  onCropOpen() {
    set({ isCropOpen: true });
  },
  onCropClose() {
    set({ isCropOpen: false });
  },
  cropProps: {
    onCropComplete: () => {},
    image: '',
    aspect: 1,
    minZoom: 1,
  },
  setCropperProps(props: CropProps) {
    set({ cropProps: props });
  },
}));
