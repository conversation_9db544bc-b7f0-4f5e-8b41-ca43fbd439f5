import { type ClassValue, clsx } from 'clsx';
import Compressor from 'compressorjs';
import moment from 'moment';
import { twMerge } from 'tailwind-merge';
import { IMAGE_COMPRESS_SIZE, IMAGE_COMPRESS_SIZE_10MB, IMAGE_COMPRESS_SIZE_5MB } from '@/consts/sizes';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getTheLongerSide = (width: number, height: number) => {
  return width > height ? width : height;
};

type DateFormat = 'input' | 'display' | 'jp';

export const formatDateTime = (dateString?: Date | string, format: DateFormat = 'input') => {
  try {
    const m = moment(dateString || new Date());

    switch (format) {
      case 'display':
        return m.format('YYYY/MM/DD HH:mm');
      case 'jp':
        return m.format('YYYY年MM月DD日 HH:mm');
      case 'input':
      default:
        return m.format('YYYY-MM-DDTHH:mm');
    }
  } catch {
    return '';
  }
};

export const formatDuration = (seconds: number): string => {
  const duration = moment.duration(seconds, 'seconds');
  const minutes = duration.minutes().toString().padStart(2, '0');
  const secs = duration.seconds().toString().padStart(2, '0');
  return `${minutes}:${secs}`;
};

export const getExt = (fileName: string) => {
  return fileName ? `.${fileName.split('.').pop()}` : '';
};

export const compressImage = async (image: File | Blob, convertToWebp = false) => {
  if (image.size > IMAGE_COMPRESS_SIZE) {
    let quality = 0.6;
    switch (true) {
      case image.size > IMAGE_COMPRESS_SIZE_10MB:
        quality = 0.2;
        break;
      case image.size > IMAGE_COMPRESS_SIZE_5MB:
        quality = 0.5;
        break;
      default:
        break;
    }
    return new Promise<File>((resolve, reject) => {
      new Compressor(image, {
        quality,
        mimeType: convertToWebp ? 'image/webp' : 'auto',
        success: (result) => {
          resolve(result as File);
        },
        error: (err) => {
          reject(err);
        },
      });
    });
  }
  return image;
};

export const arrayToObject = (array: any[], key: string) => {
  return array.reduce((acc, item) => {
    if (!acc[item[key]]) {
      acc[item[key]] = [];
    }
    acc[item[key]].push(item);
    return acc;
  }, {});
};

export const bytesToMB = (bytes: number): number => {
  return bytes / (1024 * 1024);
};

export const getUserIdentityId = (identityId: string) => {
  const decodedId = decodeURIComponent(identityId);
  if (decodedId.startsWith('@')) {
    const cleanId = decodedId.substring(1);
    return cleanId;
  }
  return decodedId;
};

export const resetEscapedValue = (value: string) => {
  // すべての特殊文字をエスケープ処理
  const escapedValue = value
    .replace(/\\/g, '\\\\') // バックスラッシュ
    .replace(/\./g, '\\.') // ドット
    .replace(/\//g, '\\/') // スラッシュ
    .replace(/\.\./g, '\\.\\.') // 連続ドット
    .replace(/~/g, '\\~') // チルダ
    .replace(/\$/g, '\\$') // ドル記号（環境変数）
    .replace(/\*/g, '\\*') // アスタリスク（ワイルドカード）
    .replace(/\?/g, '\\?') // クエスチョンマーク（単一文字ワイルドカード）
    .replace(/\[/g, '\\[') // 左角括弧（文字集合開始）
    .replace(/\]/g, '\\]') // 右角括弧（文字集合終了）
    .replace(/\{/g, '\\{') // 左波括弧（展開）
    .replace(/\}/g, '\\}') // 右波括弧（展開）
    .replace(/\(/g, '\\(') // 左丸括弧
    .replace(/\)/g, '\\)') // 右丸括弧
    .replace(/!/g, '\\!') // 感嘆符
    .replace(/\^/g, '\\^') // キャレット
    .replace(/\+/g, '\\+') // プラス記号
    .replace(/\|/g, '\\|') // パイプ記号
    .replace(/&/g, '\\&') // アンパサンド
    .replace(/;/g, '\\;') // セミコロン
    .replace(/>/g, '\\>') // 大なり記号
    .replace(/</g, '\\<') // 小なり記号
    .replace(/"/g, '\\"') // ダブルクォート
    .replace(/'/g, "\\'") // シングルクォート
    .replace(/`/g, '\\`'); // バッククォート
  return escapedValue;
};

export const randomString = () => {
  return Math.random().toString(36).slice(-8);
};

// 全角数字を半角数字に変換
export const convertFullWidthToHalfWidth = (input: string) => {
  return input.replace(/[[０-９]]/g, (char) => {
    return String.fromCharCode(char.charCodeAt(0) - 0xfee0);
  });
};

export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};
