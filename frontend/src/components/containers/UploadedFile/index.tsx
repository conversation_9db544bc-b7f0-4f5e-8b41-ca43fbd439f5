'use client';

import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import TextInput from '@/components/atoms/inputs/text-input';
import Thumbnail from '@/components/containers/Thumbnail';
import ShopPublicImage from '@/components/ShopImage';
import { useExhibitsStore } from '@/store/useExhibit';
import { extractBenefitFiles } from '@/utils/extractDatas';
import { isDigitalItem } from '@/utils/itemTypes';
import { GachaBenefitFile } from '@/types/gacha';
import { SingleItem } from '@/types/shopItem';

type ItemTitleInput = {
  title: string;
};

interface IUploadedItemProps {
  id: string;
  src: string;
  title: string;
  type?: 'image' | 'video' | 'audio';
  setFile?: (file: File) => void;
  setThumbImage: (image: string) => void;
  thumbnail?: string;
  handleDelete?: () => void;
  showType?: boolean;
  uploadType?: string;
  isLoading?: boolean;
  sorting?: boolean;
  progress: number;
}

const UploadedItem = ({
  src,
  title,
  type,
  setFile,
  setThumbImage,
  thumbnail,
  handleDelete,
  showType,
  id,
  uploadType,
  isLoading = false,
  sorting = false,
  progress,
}: IUploadedItemProps) => {
  const useExhibits = useExhibitsStore();
  const { exhibits, setItemFiles, setBenefits, setSamples } = useExhibits;
  const params = useParams();
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const { itemFiles = [], benefits, samples } = isDigitalItem(exhibitItem) ? exhibitItem : {};
  const [titleLength, setTitleLength] = useState(title.length);

  const {
    control,
    formState: { errors },
    watch,
  } = useForm<ItemTitleInput>({
    mode: 'onBlur',
    defaultValues: {
      title,
    },
  });
  useEffect(() => {
    setTitleLength(watch('title').length);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watch('title')]);

  const handleChangeTitle = () => {
    const benefitFiles: (SingleItem | GachaBenefitFile)[] = extractBenefitFiles(benefits);
    const categoryItems = uploadType === 'sample' ? samples : uploadType === 'benefit' ? benefitFiles : itemFiles;
    if (!categoryItems) return;
    let tempItems = [...categoryItems];
    tempItems = tempItems.map((item) => {
      if (item.id === id) {
        const tempItem = { ...item };
        tempItem.title = watch('title');
        return tempItem;
      }
      return item;
    });
    if (uploadType === 'sample') {
      setSamples(itemId, tempItems);
    } else if (uploadType === 'benefit') {
      setBenefits(itemId, { ...benefits, benefitFiles: tempItems });
    } else {
      setItemFiles(itemId, tempItems);
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center ${sorting ? 'animate-shake' : ''}`}>
      <div className="relative flex size-26 cursor-default items-center justify-center overflow-hidden rounded-lg">
        <div className="absolute left-0 top-0">
          <Thumbnail
            size="sm"
            thumbImage={thumbnail || src}
            isLoading={isLoading}
            progress={progress}
            setFile={setFile!}
            setThumbImage={setThumbImage}
            isClickUpload={false}
            isNeedCrop={false}
            originalImage=""
            /*src*/
            type={type}
            showType={showType}
          />
        </div>
        {!sorting && handleDelete && (
          <Button
            buttonType="light-small"
            buttonShape="circle"
            buttonSize="xxs"
            className="absolute right-1 top-1"
            onClick={handleDelete}
          >
            <ShopPublicImage src="/images/icons/CloseBtn.svg" width={12} height={12} alt="delete" />
          </Button>
        )}
      </div>
      <TextInput
        inputName="title"
        defaultValue={title}
        control={control}
        error={errors.title ? true : false}
        errorMsg={errors.title?.message}
        textLength={titleLength}
        maxLength={30}
        required
        className="!mb-2 mt-2 max-w-26"
        onBlur={handleChangeTitle}
        disabled={sorting}
      />
    </div>
  );
};

export default UploadedItem;
