'use client';
import { useState, useMemo } from 'react';
import { sendGTMEvent } from '@next/third-parties/google';
import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import LoadingOverlay from '@/components/containers/itemForm/LoadingOverlay';
import ShopPublicImage from '@/components/ShopImage';
import { ITEM_TYPE, ItemTypeString, ItemTypeValue } from '@/types/item';

const ICONS = {
  image: '/images/icons/PhotoIcn.svg',
  audio: '/images/icons/VoiceIcn.svg',
  video: '/images/icons/MovieIcn.svg',
} as const;

type MenuItem = {
  id: ItemTypeValue;
  itemType: ItemTypeString;
  itemTypeValue: number;
  title: string;
  description: string;
  contents: string[];
  src: string;
  alt: string;
  width?: number;
  height?: number;
};

type MenuItemCardProps = {
  isChekiExhibitable: boolean;
  identityId: string;
};
const menuItems = (isChekiExhibitable: boolean): MenuItem[] => [
  {
    id: ITEM_TYPE.DIGITAL_BUNDLE.value,
    itemType: ITEM_TYPE.DIGITAL_BUNDLE.str,
    itemTypeValue: ITEM_TYPE.DIGITAL_BUNDLE.value,
    title: 'デジタルコンテンツ',
    description: '画像/動画/音声を販売',
    contents: ['image', 'audio', 'video'],
    src: '/shop/images/mockDigitalContents.webp',
    alt: 'Digital Contents',
    width: 169,
    height: 99,
  },
  ...[
    {
      id: ITEM_TYPE.DIGITAL_GACHA.value,
      itemType: ITEM_TYPE.DIGITAL_GACHA.str,
      itemTypeValue: ITEM_TYPE.DIGITAL_GACHA.value,
      title: 'デジタルガチャ',
      description: '画像/動画/音声をガチャで販売',
      contents: ['image', 'audio', 'video'],
      src: '/shop/images/mockDigitalGacha.webp',
      alt: 'Digital Gacha',
      width: 120,
      height: 120,
    },
  ],
  ...(isChekiExhibitable
    ? [
        {
          id: ITEM_TYPE.CHEKI.value,
          itemType: ITEM_TYPE.CHEKI.str,
          itemTypeValue: ITEM_TYPE.CHEKI.value,
          title: 'チェキ風ブロマイド',
          description: '画像をチェキ風ブロマイドとして販売',
          contents: ['image'],
          src: '/shop/images/mockCheki.webp',
          alt: 'Cheki',
          width: 121,
          height: 112,
        },
      ]
    : []),
  ...[
    {
      id: ITEM_TYPE.REAL_PHOTO.value,
      itemType: ITEM_TYPE.REAL_PHOTO.str,
      itemTypeValue: ITEM_TYPE.REAL_PHOTO.value,
      title: 'プリント便',
      description: 'L版のブロマイドを販売',
      contents: ['image'],
      src: '/shop/images/mockPrintbin.webp',
      alt: 'Real Photo',
      width: 125,
      height: 96,
    },
  ],
  ...[
    {
      id: ITEM_TYPE.PRINT_GACHA.value,
      itemType: ITEM_TYPE.PRINT_GACHA.str,
      itemTypeValue: ITEM_TYPE.PRINT_GACHA.value,
      title: 'プリントガチャ',
      description: 'L版のブロマイドをガチャ形式で販売',
      contents: ['image'],
      src: '/shop/images/mockPrintGacha.webp',
      alt: 'Digital Gacha',
      width: 139,
      height: 117,
    },
  ],
];

export const MenuItemCard = ({ isChekiExhibitable, identityId }: MenuItemCardProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateItem = async (itemType: number, identityId: string) => {
    setIsLoading(true);
    const tempItemId = 'new' + Date.now();
    let eventType = 'fanme_listing_digital_bundle_click';
    let url = `/@${identityId}/item/${tempItemId}/create`;

    switch (itemType) {
      case ITEM_TYPE.DIGITAL_GACHA.value:
        eventType = 'fanme_listing_digital_gacha_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=${ITEM_TYPE.DIGITAL_GACHA.str}`;
        break;
      case ITEM_TYPE.CHEKI.value:
        eventType = 'fanme_listing_cheki_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=${ITEM_TYPE.CHEKI.str}`;
        break;
      case ITEM_TYPE.PRINT_GACHA.value:
        eventType = 'fanme_listing_print_gacha_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=${ITEM_TYPE.PRINT_GACHA.str}`;
        break;
      case ITEM_TYPE.REAL_PHOTO.value:
        eventType = 'fanme_listing_real_photo_click';
        url = `/@${identityId}/item/${tempItemId}/create?item_type=${ITEM_TYPE.REAL_PHOTO.str}`;
        break;
      default:
        eventType = 'fanme_listing_digital_bundle_click';
        url = `/@${identityId}/item/${tempItemId}/create`;
        break;
    }

    sendGTMEvent({ event: eventType, shop_id: identityId });
    try {
      router.push(url);
    } finally {
      setIsLoading(false);
    }
  };

  const contentsIcons = (contents: string[]) => {
    return contents.map((content: string, index: number) => {
      return (
        <ShopPublicImage key={index} src={ICONS[content as keyof typeof ICONS]} width={24} height={24} alt={content} />
      );
    });
  };

  return (
    <>
      {isLoading && <LoadingOverlay />}
      <div className="mx-auto max-w-120 space-y-4 p-4" style={{ height: 'calc(100vh - 56px)' }}>
        {menuItems(isChekiExhibitable).map((item) => (
          <div
            key={item.id}
            className="relative flex cursor-pointer flex-row items-center justify-between rounded-md bg-white px-4 py-2"
            onClick={() => handleCreateItem(item.itemTypeValue ?? 0, identityId)}
            style={{
              backgroundImage: `url(${item.src})`,
              backgroundSize: `${item.width}px ${item.height}px`,
              backgroundPosition: 'right 14px center',
              backgroundRepeat: 'no-repeat',
            }}
          >
            <div className="max-100:pl-1 flex min-w-0 flex-col items-start gap-2 p-[0.9vw]">
              <div className="text-bold-16 underline decoration-yellow-100 decoration-10 underline-offset-[-4px]">
                {item.title}
              </div>
              <div className="flex-nowrap text-medium-11">{item.description}</div>
              <div className="flex w-28 justify-center gap-1">{contentsIcons(item.contents)}</div>
              <button
                onClick={() => handleCreateItem(item.itemTypeValue ?? 0, identityId)}
                className="h-8 w-28 rounded-2xl bg-black text-[12px] font-bold text-white"
              >
                出品する
              </button>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default MenuItemCard;
