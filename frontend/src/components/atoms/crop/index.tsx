'use client';

import React, { useState, useCallback, ReactNode } from 'react';
import <PERSON><PERSON>per, { CropperProps, Area } from 'react-easy-crop';
import Button from '@/components/atoms/button';
import getCroppedImg from '@/components/atoms/crop/cropImage';

type CropProps = Pick<CropperProps, 'image' | 'onMediaLoaded'>;

interface ICropProps extends CropProps {
  onCropComplete?: (image: string) => void;
  onCancel?: () => void;
  onCloseCrop: () => void;
  minZoom?: number;
  aspect?: number;
  maskText?: ReactNode;
  showAspectToggle?: boolean;
  borderColor?: string;
}

const Crop = ({
  image,
  onCropComplete,
  onMediaLoaded,
  onCancel,
  onCloseCrop,
  minZoom,
  aspect = 1,
  maskText,
  showAspectToggle = false,
  borderColor = '#fff',
}: ICropProps) => {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [currentAspect, setCurrentAspect] = useState(aspect);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area>({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });

  // マウスかドラグの動作を終わったら、座標を保存する
  const onComplete = useCallback((croppedAreaPercentages: Area, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const showCroppedImage = async () => {
    try {
      const croppedImage = (await getCroppedImg(image!, croppedAreaPixels)) as string;
      if (onCropComplete) {
        onCropComplete(croppedImage);
      }
    } catch (e) {
      console.error(e);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onCloseCrop();
  };

  const toggleAspectRatio = (newAspect: number) => {
    setCurrentAspect(newAspect);
  };

  return (
    <div className="fixed top-0 z-1000 flex h-screen w-full max-w-120 flex-col bg-[url('/shop/images/empty.svg')] bg-repeat">
      {/* 上部のボタン */}
      <div className="z-50 flex items-center justify-between p-4">
        <Button
          buttonType="light"
          buttonShape="rounded"
          onClick={handleCancel}
          buttonSize="md"
          buttonClassNames="h-12 px-6 text-bold-16"
        >
          キャンセル
        </Button>
        <Button
          buttonType="dark"
          buttonShape="rounded"
          onClick={showCroppedImage}
          buttonSize="md"
          buttonClassNames="h-12 px-6 text-bold-16"
        >
          決定
        </Button>
      </div>

      {/* マスクテキスト */}
      {!!maskText && (
        <div className="absolute left-1/2 top-20 z-50 -translate-x-1/2 transform px-4 text-center text-sm text-white">
          {maskText}
        </div>
      )}

      {/* クロップエリア */}
      <div className="absolute left-1/2 top-0 z-40 h-full w-full flex-1 -translate-x-1/2 transform">
        <Cropper
          minZoom={minZoom}
          image={image}
          onCropComplete={onComplete}
          onZoomChange={setZoom}
          crop={crop}
          zoom={zoom}
          aspect={currentAspect}
          onCropChange={setCrop}
          onMediaLoaded={onMediaLoaded}
          style={{
            cropAreaStyle: {
              borderWidth: 3,
              borderColor,
            },
          }}
          classes={{ cropAreaClassName: 'w-11/12' }}
          showGrid={false}
          zoomSpeed={2}
        />
      </div>

      {/* 下部のアスペクト比変更ボタン */}
      {showAspectToggle && (
        <div className="z-50 flex items-center justify-center space-x-4 p-4">
          <Button
            buttonType={currentAspect === 3 / 4 ? 'primary' : 'light-shadow'}
            buttonShape="rounded"
            onClick={() => toggleAspectRatio(3 / 4)}
            buttonSize="free"
            buttonClassNames="h-10 px-4 text-sm"
          >
            <div className="h-8 w-6 rounded-sm border-2 border-current"></div>
          </Button>
          <Button
            buttonType={currentAspect === 1 ? 'primary' : 'light-shadow'}
            buttonShape="rounded"
            onClick={() => toggleAspectRatio(1)}
            buttonSize="free"
            buttonClassNames="h-10 px-4 text-sm"
          >
            <div className="h-6 w-6 rounded-sm border-2 border-current"></div>
          </Button>
          <Button
            buttonType={currentAspect === 4 / 3 ? 'primary' : 'light-shadow'}
            buttonShape="rounded"
            onClick={() => toggleAspectRatio(4 / 3)}
            buttonSize="free"
            buttonClassNames="h-10 px-4 text-sm"
          >
            <div className="h-6 w-8 rounded-sm border-2 border-current"></div>
          </Button>
        </div>
      )}
    </div>
  );
};

export default Crop;
