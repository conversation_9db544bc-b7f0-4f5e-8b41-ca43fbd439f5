'use client';

import React, { useState, useCallback, ReactNode } from 'react';
import <PERSON><PERSON>per, { CropperProps, Area } from 'react-easy-crop';
import Button from '@/components/atoms/button';
import getCroppedImg from '@/components/atoms/crop/cropImage';
import Tab from '@/components/atoms/tab';
import Tabs from '@/components/atoms/tab/tabs';

type CropProps = Pick<CropperProps, 'image' | 'onMediaLoaded'>;

interface ICropProps extends CropProps {
  onCropComplete?: (image: string) => void;
  onCancel?: () => void;
  onCloseCrop: () => void;
  minZoom?: number;
  aspect?: number;
  maskText?: ReactNode;
  showAspectToggle?: boolean;
  borderColor?: string;
}

const Crop = ({
  image,
  onCropComplete,
  onMediaLoaded,
  onCancel,
  onCloseCrop,
  minZoom,
  aspect = 1,
  maskText,
  showAspectToggle = false,
  borderColor = '#fff',
}: ICropProps) => {
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [currentAspect, setCurrentAspect] = useState(aspect);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area>({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });
  // アスペクト比に基づいてタブインデックスを計算
  const getTabIndexFromAspect = (aspectRatio: number) => {
    if (aspectRatio === 3 / 4) return 0;
    if (aspectRatio === 4 / 3) return 1;
    return 0;
  };

  const [activeTabIndex, setActiveTabIndex] = useState(getTabIndexFromAspect(aspect));

  // マウスかドラグの動作を終わったら、座標を保存する
  const onComplete = useCallback((_croppedAreaPercentages: Area, croppedAreaPixels: Area) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const showCroppedImage = async () => {
    try {
      const croppedImage = (await getCroppedImg(image!, croppedAreaPixels)) as string;
      if (onCropComplete) {
        onCropComplete(croppedImage);
      }
    } catch (e) {
      console.error(e);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    onCloseCrop();
  };

  const toggleAspectRatio = (newAspect: number) => {
    setCurrentAspect(newAspect);
    setActiveTabIndex(getTabIndexFromAspect(newAspect));
  };

  const handleTabChange = (index: number) => {
    const aspectRatios = [3 / 4, 4 / 3];
    const newAspect = aspectRatios[index];
    if (newAspect) {
      toggleAspectRatio(newAspect);
    }
  };

  return (
    <div className="fixed top-0 z-1000 flex h-screen w-full max-w-120 flex-col bg-[url('/shop/images/empty.svg')] bg-repeat">
      {/* 上部のボタン */}
      <div className="z-50 flex items-center justify-between p-4">
        <Button
          buttonType="light"
          buttonShape="rounded"
          onClick={handleCancel}
          buttonSize="md"
          buttonClassNames="h-12 px-6 text-bold-16"
        >
          キャンセル
        </Button>
        <Button
          buttonType="dark"
          buttonShape="rounded"
          onClick={showCroppedImage}
          buttonSize="md"
          buttonClassNames="h-12 px-6 text-bold-16"
        >
          決定
        </Button>
      </div>

      {/* マスクテキスト */}
      {!!maskText && (
        <div className="absolute left-1/2 top-20 z-50 w-full -translate-x-1/2 px-4 text-left text-sm text-white">
          {maskText}
        </div>
      )}

      {/* クロップエリア */}
      <div className="absolute left-1/2 top-0 z-40 size-full flex-1 -translate-x-1/2">
        <Cropper
          minZoom={minZoom}
          image={image}
          onCropComplete={onComplete}
          onZoomChange={setZoom}
          crop={crop}
          zoom={zoom}
          aspect={currentAspect}
          onCropChange={setCrop}
          onMediaLoaded={onMediaLoaded}
          style={{
            cropAreaStyle: {
              borderWidth: 3,
              borderColor,
            },
          }}
          classes={{ cropAreaClassName: 'w-11/12' }}
          showGrid={false}
          zoomSpeed={2}
        />
      </div>

      {/* 下部のアスペクト比変更タブ */}
      {showAspectToggle && (
        <div className="z-50 flex items-center justify-center p-4">
          <Tabs value={activeTabIndex} onChange={handleTabChange} type="slide">
            <Tab>
              <div className="h-8 w-6 rounded-sm border-2 border-current bg-white"></div>
            </Tab>
            <Tab>
              <div className="h-6 w-8 rounded-sm border-2 border-current bg-white"></div>
            </Tab>
          </Tabs>
        </div>
      )}
    </div>
  );
};

export default Crop;
