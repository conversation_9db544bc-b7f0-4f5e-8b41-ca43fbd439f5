'use client';
import React, { useState, useEffect } from 'react';
import { clsx } from 'clsx';
import { TabsProps, TabChildProps } from './tab.type';

const Tabs = ({ className, value, children, onChange, type = 'normal', disable, width, height }: TabsProps) => {
  const [activeIndex, setActiveIndex] = useState(value || 0);
  const handleTabChange = (index: number) => {
    onChange(index);
  };

  useEffect(() => {
    setActiveIndex(value);
  }, [value]);

  // デフォルトのサイズを設定
  const defaultSize = type === 'slide' ? 'h-9 w-36' : type === 'effect' ? 'h-10 w-full' : 'h-8 w-86';
  const customSize = width || height ? `${height || ''} ${width || ''}`.trim() : '';
  const finalSize = customSize || defaultSize;

  return (
    <div
      className={clsx(
        'relative flex justify-between',
        type === 'slide'
          ? `${finalSize} items-center justify-between rounded-full bg-gray-200 text-medium-13 text-white`
          : type === 'effect'
            ? `${finalSize} rounded-4xl border-navy-50 text-secondary`
            : finalSize,
        disable ? 'opacity-50' : '',
        className,
      )}
    >
      {type === 'slide' && (
        // slider div
        <div
          className={clsx(
            'absolute h-full w-1/2 rounded-full border-2 border-gray-600 bg-gray-800 transition-all duration-300 ease-in-out',
            activeIndex === 0 ? 'left-0' : 'left-1/2',
          )}
        />
      )}
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child as React.ReactElement<TabChildProps>, {
            isActive: value === index,
            type: type,
            position: index === 0 ? 'left' : index === children.length - 1 ? 'right' : undefined,
            onClick: () => {
              if (disable) return;
              setActiveIndex(index);
              handleTabChange(index);
            },
          });
        }
        return null;
      })}
    </div>
  );
};

export default Tabs;
