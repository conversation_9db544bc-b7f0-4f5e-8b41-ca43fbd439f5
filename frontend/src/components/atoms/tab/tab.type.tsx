import { ReactElement } from 'react';

type TabType = 'normal' | 'slide' | 'effect';
interface TabProps {
  label?: string;
  children?: ReactElement;
  ref?: React.MutableRefObject<null>;
  isActive?: boolean;
  onClick?: () => void;
  type?: TabType;
  position?: 'left' | 'right';
}

type TabsProps = {
  tabRefs?: React.MutableRefObject<null>[];
  children: ReactElement[];
  value: number;
  onChange: (newValue: number) => void;
  className?: string;
  type?: TabType;
  disable?: boolean;
  width?: string;
  height?: string;
};

interface TabChildProps {
  isActive?: boolean;
  onClick?: () => void;
  type?: TabType;
  position?: 'left' | 'right';
}

export type { TabProps, TabType, TabsProps, TabChildProps };
