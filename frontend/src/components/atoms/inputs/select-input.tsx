'use client';

import React from 'react';
import { Control, Controller, FieldValues, Path } from 'react-hook-form';
import clsx from 'clsx';

type SelectInputProps<T extends FieldValues> = {
  control: Control<T>;
  options: { value: string | number; label?: string }[];
  labelTitle?: string;
  inputName: Path<T>;
  required?: boolean;
  maxLength?: number;
  errorMsg?: string;
  errorMsgClassName?: string;
  placeholder?: string;
  textLength?: number;
  className?: string;
  inputClassName?: string;
  showMaxLength?: boolean;
  labelClassName?: string;
  disabled?: boolean;
  defaultValue?: string;
};

const SelectInput = <T extends FieldValues>({
  control,
  options,
  labelTitle,
  inputName,
  required,
  errorMsg,
  errorMsgClassName,
  className,
  inputClassName,
  labelClassName,
  disabled,
  defaultValue,
}: SelectInputProps<T>) => {
  return (
    <div className={clsx('relative mb-5', className)}>
      {labelTitle && (
        <label
          htmlFor={inputName}
          className={clsx(
            'mb-2 flex min-w-20 items-center justify-between text-medium-13 text-secondary',
            labelClassName,
          )}
        >
          <span>
            {labelTitle}
            {required && <span className="ml-0.5 text-regular-11 text-orange-200">*必須</span>}
          </span>
        </label>
      )}
      <Controller
        name={inputName}
        rules={{ required }}
        control={control}
        render={({ field }) => (
          <>
            <select
              {...field}
              value={field.value ?? defaultValue ?? ''}
              disabled={disabled}
              onChange={(e) =>
                field.onChange(typeof options[0]?.value === 'number' ? Number(e.target.value) : e.target.value)
              }
              className={clsx(
                inputClassName,
                'h-8 w-full appearance-none rounded-lg border-none bg-white px-3 text-medium-13',
                disabled && 'cursor-not-allowed bg-gray-50 text-gray-400',
              )}
            >
              {options.map((v, _) => {
                return (
                  <option key={`opt-${v.value}`} value={v.value}>
                    {v.label || v.value}
                  </option>
                );
              })}
            </select>
            <div className="pointer-events-none absolute bottom-1 right-0 flex items-center">
              <svg className="size-6 fill-black" viewBox="0 0 20 20">
                <path d="M5.25 7.5L10 12.5L14.75 7.5H5.25Z" />
              </svg>
            </div>
          </>
        )}
      />
      {!!errorMsg && (
        <p className={clsx('col-start-1 col-end-4 mt-1 text-right text-regular-10 text-error', errorMsgClassName)}>
          {errorMsg}
        </p>
      )}
    </div>
  );
};

export default SelectInput;
