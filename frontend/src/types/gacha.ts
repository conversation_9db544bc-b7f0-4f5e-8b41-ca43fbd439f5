import type { BaseItemFile, MediaType, BaseBenefit, BaseShopItemDetail } from './shopItem';
import type { GachaFile } from '@/app/api/types/backend/shop/ShopItem';

export const AWARD_TYPE = {
  S: 4,
  A: 3,
  B: 2,
  C: 1,
} as const;
export type Award = (typeof AWARD_TYPE)[keyof typeof AWARD_TYPE];
export const MAX_AWARD_COUNT = Object.keys(AWARD_TYPE).length;
export type AwardProbability = {
  awardType: Award;
  probability: number;
};

// Gacha item type with specific fields for digital gacha
export interface GachaItemFile extends BaseItemFile {
  sortOrder: number | null;
  awardType: Award | null;
  isSecret: boolean | null;
  receivedFileCount?: number;
  isNew?: boolean;
  maskedThumbnailUri?: string;
}

export type GachaBenefitFile = {
  id: string;
  title: string;
  type: MediaType;
  src: string;
  thumbnail: string;
  size: number;
  duration: number;
  sortOrder: null;
  awardType: null;
  isSecret: null;
};

// Gacha benefit with gacha-specific files
export type GachaBenefitItem = {
  conditionType: number;
  benefitFile: GachaBenefitFile;
};
export interface GachaBenefit extends BaseBenefit {
  benefit: GachaBenefitItem[];
}

// Gacha shop item detail

export type GachaItemContent = Omit<BaseShopItemDetail['item'], 'thumbnailType'> & {
  totalCapacity: number;
  collectedUniqueItemsCount: number;
  remainingUniquePullCount: number;
  isCompleted: boolean;
  duplicatedCount: number;
  itemFiles: GachaItemFile[];
  samples?: GachaItemFile[];
  benefits?: GachaBenefit;
  isDuplicated: boolean;
  awardProbabilities: AwardProbability[];
  thumbnailType: 'custom';
};
export interface GachaItem extends BaseShopItemDetail {
  item: GachaItemContent;
}

export interface PullGachaResponse extends GachaItem {
  currentPulledFiles: GachaItemFile[];
  selectedAwardFileId?: string | null;
}

export type GachaFileWithCount = GachaFile & { count?: number };
