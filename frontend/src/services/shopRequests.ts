import HttpClient from '@/utils/request';
import { ApiResponse, CreateShopRequest, CreateShopResponse } from '@/types/api';
import { Shop } from '@/types/shopinfo';

export const shopRequests = {
  createShop: async (data: CreateShopRequest): Promise<ApiResponse<CreateShopResponse>> => {
    const response = await HttpClient.post<CreateShopResponse>('/api/shop/create', data);
    return response;
  },
  updateShop: async (data: CreateShopRequest): Promise<ApiResponse<Shop>> => {
    const response = await HttpClient.put<Shop>('/api/shop/update', data);
    return response;
  },
  getShopMarginRate: async (): Promise<ApiResponse<{ marginRate: number }>> => {
    const response = await HttpClient.get<{ marginRate: number }>('/api/shop/margin');
    return response;
  },
  getShopInfo: async (identityId: string): Promise<ApiResponse<{ shop: Shop }>> => {
    const response = await HttpClient.get<{ shop: Shop }>(`/api/shop/shops/${identityId}`);
    return response;
  },
};
