'use server';
import createError from 'http-errors';
import { NextResponse } from 'next/server';
import {
  ApiAwardProbability,
  ChekiItemDetailContent,
  GachaBenefitFile,
  GachaFile,
  GachaItemDetailContent,
  ShopItemDetailBk,
  ShopItemDetailContent,
  SingleFile,
} from '@/app/api/types/backend/shop/ShopItem';
import { ServerFetchClient } from '@/services/server/fetch-client';
import { ApiResponse } from '@/types/api';
import { GachaItem } from '@/types/gacha';
import { ITEM_TYPE } from '@/types/item';
import { ChekiItemDetail, MediaType, ShopItemDetail } from '@/types/shopItem';

const sortItemFiles = (files: SingleFile[], isViewer?: boolean) => {
  // First, separate images from other file types
  const imageFiles: SingleFile[] = [];
  const otherFiles: SingleFile[] = [];

  files.forEach((file) => {
    if (file.file_type === 'image') {
      // When isViewer is true, only add purchased images to imageFiles
      if (!isViewer || file.is_purchased) {
        imageFiles.push(file);
      }
    } else {
      otherFiles.push(file);
    }
  });

  // Transform all files with appropriate properties
  const transformedFiles = files.map((file: SingleFile) => {
    const isImage = file.file_type === 'image';
    // Find the index of this image in the imageFiles array if it's an image
    // For non-purchased images when isViewer is true, this will be -1
    const imageIndex = isImage ? imageFiles.findIndex((img) => img.id === file.id) : -1;

    return {
      id: file.id,
      title: file.name,
      src: file.object_uri,
      thumbnail: file.thumbnail_uri,
      thumbnailRatio: 1,
      type: file.file_type,
      duration: file.duration,
      price: file.price,
      currentPrice: file.current_price,
      size: file.size,
      isPurchased: file.is_purchased,
      isCheckout: file.is_checkout,
      selected: file.item_thumbnail_selected,
      // Add sortIndex for images only if they are in the imageFiles array
      sortIndex: imageIndex >= 0 ? imageIndex : undefined,
      awardType: file.award_type,
      isSecret: file.is_secret,
      receivedFileCount: file.received_file_count,
    };
  });

  return transformedFiles;
};

const formatShopItemDetail = async <T extends ShopItemDetailContent | GachaItemDetailContent>(
  item: T,
  isEditor?: boolean,
) => {
  // 共通部分を抽出
  const baseFields = {
    id: String(item.id || ''),
    title: item.name,
    description: item.description,
    thumbnail: item.thumbnail_uri,
    thumbnailRatio: item.thumbnail_ratio,
    price: item.price,
    available: !!item.available,
    itemType: item.item_type,
    tags: item.tags,
    isPurchased: item.is_purchased,
    isCheckout: item.is_checkout,
    purchasedCount: item.purchased_count,
  };

  // 共通の item_option 派生フィールド
  const option = item.item_option || {};
  const sharedOptionFields = {
    hasPassword: !!option.password,
    password: option.password ?? '',
    period: option.for_sale
      ? {
          start: option.for_sale.start_at ? new Date(option.for_sale.start_at) : undefined,
          end: option.for_sale.end_at ? new Date(option.for_sale.end_at) : undefined,
        }
      : undefined,
    discount: option.on_sale
      ? {
          percentage: option.on_sale.discount_rate ? option.on_sale.discount_rate * 100 : undefined,
          start: new Date(option.on_sale.start_at || ''),
          end: new Date(option.on_sale.end_at || ''),
        }
      : undefined,
  };

  if (item.item_type === ITEM_TYPE.DIGITAL_GACHA.value) {
    const gachaItem = item as GachaItemDetailContent;
    // Calculate totalCapacity and completedCount (sum of items with count > 0)
    const { totalCapacity, completedCount, duplicatedCount } = (gachaItem.files || []).reduce(
      (acc, file) => {
        return {
          totalCapacity: acc.totalCapacity + (file.size || 0),
          completedCount: acc.completedCount + (file.received_file_count && file.received_file_count > 0 ? 1 : 0),
          duplicatedCount: acc.duplicatedCount + (file.received_file_count && file.received_file_count > 1 ? 1 : 0),
        };
      },
      { totalCapacity: 0, completedCount: 0, duplicatedCount: 0 },
    );

    const itemFiles = (gachaItem.files || []).map((file) => ({
      id: String(file.id),
      title: file.name,
      type: file.file_type as MediaType,
      src: file.object_uri && (!!file.received_file_count || isEditor) ? file.object_uri : '',
      thumbnail:
        file.file_type === 'audio' || (!!file.thumbnail_uri && (!!file.received_file_count || isEditor))
          ? file.thumbnail_uri
          : '',
      maskedThumbnailUri: file.file_type === 'audio' ? file.thumbnail_uri : file.masked_thumbnail_uri,
      thumbnailRatio: 1,
      duration: file.duration,
      size: file.size,
      sortOrder: file.sort_order,
      awardType: file.award_type,
      isSecret: file.is_secret,
      receivedFileCount: file.received_file_count,
    }));
    const gachaFields = {
      ...sharedOptionFields,
      totalCapacity,
      completedCount,
      duplicatedCount,
      currentPrice: gachaItem.current_price,
      collectedUniqueItemsCount: completedCount,
      isCompleted: gachaItem.is_completed,
      itemFiles,
      samples: ((gachaItem.samples || []) as GachaFile[]).map((file: GachaFile) => ({
        id: String(file.id),
        title: file.name,
        type: file.file_type as MediaType,
        src: file.object_uri,
        thumbnail: file.thumbnail_uri || '',
        size: file.size,
        duration: file.duration,
        sortOrder: file.sort_order,
        awardType: file.award_type,
        isSecret: false,
      })),
      benefits: gachaItem.benefits
        ? {
            description: gachaItem.benefits.description || '',
            benefit: (gachaItem.benefits.files || []).map((file: GachaBenefitFile) => ({
              conditionType: file.condition_type || 0,
              benefitFile: {
                id: String(file.id),
                title: file.name,
                type: file.file_type as MediaType,
                src: file.object_uri,
                thumbnail: file.thumbnail_uri || '',
                size: file.size || 0,
                duration: file.duration || 0,
                sortOrder: null,
                awardType: null,
                isSecret: null,
              },
            })),
          }
        : undefined,
      isDuplicated: gachaItem.is_duplicated_digital_gacha_items,
      remainingUniquePullCount: gachaItem.remaining_unique_pull_count,
      awardProbabilities: gachaItem.award_probabilities.map((prob: ApiAwardProbability) => ({
        awardType: prob.award_type,
        probability: prob.probability,
      })),
      thumbnailType: 'custom' as const,
      thumbnailBlurLevel: gachaItem.thumbnail_blur_level,
      thumbnailWatermarkLevel: gachaItem.thumbnail_watermark_level,
    };
    return {
      item: { ...baseFields, ...gachaFields },
    } as GachaItem;
  } else if (item.item_type === ITEM_TYPE.DIGITAL_BUNDLE.value) {
    const digitalBundleItem = item as ShopItemDetailContent;
    const itemFiles = sortItemFiles(digitalBundleItem.files, false);
    const normalFields = {
      ...sharedOptionFields,
      singleSale: option.is_single_sales,
      currentPrice: digitalBundleItem.current_price,
      discountRate: option.on_sale ? option.on_sale.discount_rate * 100 : undefined,
      itemFiles,
      samples: digitalBundleItem.samples?.map((file: SingleFile) => ({
        id: file.id,
        title: file.name,
        src: file.object_uri,
        thumbnail: file.thumbnail_uri,
        type: file.file_type,
        duration: file.duration,
        price: file.price,
        currentPrice: file.current_price,
        size: file.size,
        isPurchased: file.is_purchased,
      })),
      benefits: digitalBundleItem.benefit
        ? {
            description: digitalBundleItem.benefit.description,
            benefitFiles: digitalBundleItem.benefit.files.map((file: SingleFile) => ({
              id: file.id,
              title: file.name,
              src: file.object_uri,
              thumbnail: file.thumbnail_uri,
              type: file.file_type,
              duration: file.duration,
              price: file.price,
              currentPrice: file.current_price,
              size: file.size,
              isPurchased: file.is_purchased,
            })),
          }
        : undefined,
      limited: option.qty_total || undefined,
      limitedPerUser: option.qty_per_user || undefined,
      remainingAmount: option.remaining_amount != null ? option.remaining_amount : undefined,
      thumbnailType: digitalBundleItem.thumbnail_from ? 'upload' : 'custom',
      thumbnailBlurLevel: digitalBundleItem.thumbnail_blur_level,
      thumbnailWatermarkLevel: digitalBundleItem.thumbnail_watermark_level,
    };
    return { item: { ...baseFields, ...normalFields } } as ShopItemDetail;
  } else {
    const chekiItem = item as ChekiItemDetailContent;
    const normalFields = {
      ...sharedOptionFields,
      currentPrice: chekiItem.current_price,
      discountRate: option.on_sale ? option.on_sale.discount_rate * 100 : undefined,
      samples: chekiItem.samples?.map((file: SingleFile) => ({
        id: file.id,
        title: file.name,
        src: file.object_uri,
        thumbnail: file.thumbnail_uri,
        type: file.file_type,
        duration: file.duration,
        price: file.price,
        currentPrice: file.current_price,
        size: file.size,
        isPurchased: file.is_purchased,
      })),
      benefits: chekiItem.benefit
        ? {
            description: chekiItem.benefit.description,
            benefitFiles: chekiItem.benefit.files.map((file: SingleFile) => ({
              id: file.id,
              title: file.name,
              src: file.object_uri,
              thumbnail: file.thumbnail_uri,
              type: file.file_type,
              duration: file.duration,
              price: file.price,
              currentPrice: file.current_price,
              size: file.size,
              isPurchased: file.is_purchased,
            })),
          }
        : undefined,
      limited: option.qty_total || undefined,
      limitedPerUser: option.qty_per_user,
      remainingAmount: option.remaining_amount != null ? option.remaining_amount : undefined,
      thumbnailType: 'custom',
      thumbnailBlurLevel: 0,
      thumbnailWatermarkLevel: 0,
    };
    return { item: { ...baseFields, ...normalFields } } as ChekiItemDetail;
  }
};

export const getItem = async (
  id: string,
  identityId: string,
  isViewer?: boolean,
  isEditor?: boolean,
): Promise<NextResponse<ApiResponse<ShopItemDetail | GachaItem | ChekiItemDetail>>> => {
  const fetchClient = new ServerFetchClient();
  try {
    const addParam = isViewer ? '?include_deleted=1' : '';
    const url: string = isEditor
      ? `/shops/current/items/${id}${addParam}`
      : `/shops/${identityId}/items/${id}${addParam}`;
    const res: ApiResponse<ShopItemDetailBk> = await fetchClient.get(url, {
      cache: 'no-store',
    });

    if (!res.data?.item) {
      return NextResponse.json({ error: 'Item not found', statusText: 'Not Found' }, { status: 404 });
    }
    const resData = res.data;

    // 他人の商品の場合は403エラー
    if (isEditor && resData.item.creator_account_identity !== identityId) {
      return NextResponse.json({ error: 'Is not shop owner access', statusText: 'Forbidden' }, { status: 403 });
    }

    const item = await formatShopItemDetail(resData.item, isEditor);
    return NextResponse.json({ data: item, statusText: 'success' });
  } catch (error: any) {
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ error: error.message, statusText: 'Internal Server Error' }, { status: 500 });
  }
};

export const checkItemPasswordUnlockCache = async (
  identityId: string,
  itemId: number,
): Promise<ApiResponse<boolean>> => {
  const fetchClient = new ServerFetchClient();
  try {
    const res = await fetchClient.get(`/shops/${identityId}/items/${itemId}/password-unlock`);
    return { data: res.data !== null, statusText: 'success' };
  } catch (error: any) {
    if (error.response) {
      console.error('Error response:', {
        status: error.response.status,
        data: error.response.data,
      });
    } else {
      console.error(JSON.stringify({ error }));
    }
    throw createError(500, error.message);
  }
};

export const createItemPasswordUnlockCache = async (
  identityId: string,
  itemId: number,
  password: string,
): Promise<ApiResponse<boolean>> => {
  const fetchClient = new ServerFetchClient();
  try {
    await fetchClient.post(`/shops/${identityId}/items/${itemId}/password-unlock`, {
      user_input_password: password,
    });
    return { data: true, statusText: 'success' };
  } catch (error: any) {
    console.error(JSON.stringify({ error }));
    throw createError(500, error.message);
  }
};
