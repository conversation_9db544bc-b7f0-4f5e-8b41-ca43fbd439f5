export type Shop = {
  id?: string;
  name: string;
  message?: string;
  description?: string;
  headerImageUri?: string;
  creatorIconUri?: string;
  creatorName?: string;
  limitation: ShopLimitation;
};

export type ShopLimitation = {
  fileCapacity: number;
  fileQuantity: number;
};

export type ShopServerResponse = {
  name: string;
  tenant: string;
  creator_uid: string;
  creator_name: string;
  creator_icon_uri: string;
  description?: string;
  header_image_uri?: string;
  message: string;
  margin_rate: number;
  is_open: boolean;
  limitation: ShopServerLimitation;
};

export type ShopServerLimitation = {
  file_capacity: number;
  file_quantity: number;
};
