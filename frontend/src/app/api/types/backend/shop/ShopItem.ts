import { AwardProbability } from '@/types/gacha';

export type ShopItemBk = {
  id: number;
  name: string;
  description: string;
  thumbnail_uri: string;
  price: number;
  current_price: number;
  min_price?: number;
  file_type: string;
  item_type: number;
  available: boolean;
  has_benefit: boolean;
  tags: string[];
  item_option: ItemOption;
  is_completed: boolean;
  is_new: boolean;
  collected_unique_items_count: number;
  is_purchased: boolean;
  is_checkout: boolean;
  created_at: string;
  file_quantities?: FileQuantity[] | null;
};
export type ShopItemDetailContent = {
  id: string;
  creator_account_identity?: string;
  name: string;
  description: string;
  thumbnail_uri: string;
  thumbnail_ratio: number;
  price: number;
  current_price?: number;
  file_type: string;
  available: boolean;
  award_probabilities?: AwardProbability[];
  item_type: number;
  files: SingleFile[];
  samples?: SingleFile[];
  benefit?: Benefit;
  item_option: ItemOption;
  tags?: string[];
  is_purchased?: boolean;
  is_checkout?: boolean;
  purchased_count: number;
  is_duplicated_digital_gacha_items?: boolean;
  thumbnail_from: 0 | 1;
  thumbnail_blur_level: 0 | 1 | 2;
  thumbnail_watermark_level: 0 | 1 | 2;
};
export interface ShopItemDetailBk {
  item: ShopItemDetailContent;
}

export interface SingleFile {
  id: string;
  name: string;
  object_uri: string;
  file_type?: MediaType;
  thumbnail_uri?: string;
  masked_thumbnail_uri?: string;
  duration?: number;
  price?: number;
  current_price?: number;
  size: number;
  is_purchased?: boolean;
  is_checkout?: boolean;
  item_thumbnail_selected?: boolean;
  award_type?: number;
  condition_type?: number;
  is_secret?: boolean;
  received_file_count?: number;
}

export type MediaType = 'image' | 'video' | 'audio';

export interface Benefit {
  description?: string;
  files: SingleFile[];
}

export interface ItemOption {
  is_single_sales: boolean;
  qty_total?: number | null;
  qty_per_user?: number | null;
  remaining_amount?: number | null;
  on_sale?: {
    discount_rate: number;
    start_at?: string;
    end_at?: string;
  };
  password?: string | null;
  for_sale?: {
    start_at?: string;
    end_at?: string;
  };
}

export interface FileQuantity {
  file_type: string;
  quantity: number;
}

// Define GachaFile interface with additional gacha-specific fields
export interface GachaFile extends SingleFile {
  sort_order: number;
  award_type: 1 | 2 | 3 | 4;
  received_file_count: number;
}

// Define GachaBenefitFile interface with condition field
export interface GachaBenefitFile extends GachaFile {
  condition_type: number;
}

// Define GachaBenefit interface
export interface GachaBenefit extends Omit<Benefit, 'files'> {
  files: GachaBenefitFile[];
}

// Define AwardProbability interface
export interface ApiAwardProbability {
  award_type: 1 | 2 | 3 | 4;
  probability: number;
}

export type GachaItemDetailContent = Omit<ShopItemDetailBk['item'], 'files' | 'samples' | 'benefit'> & {
  is_duplicated: boolean;
  award_probabilities: ApiAwardProbability[];
  files: GachaFile[];
  samples?: GachaFile[];
  benefits?: GachaBenefit;
  collected_unique_items_count?: number;
  is_completed: boolean;
  remaining_unique_pull_count: number;
};

export type ChekiItemDetailContent = Omit<ShopItemDetailBk['item'], 'files'>;

// Define GachaItemBk interface extending from ShopItemDetailBk
export interface GachaItemBk extends Omit<ShopItemDetailBk, 'item'> {
  item: GachaItemDetailContent;
}

export interface FileForPullDigitalGachaItems {
  id: number;
  name: string;
  objectUri: string | null;
  thumbnailUri: string | null;
  fileType: string;
  size: number;
  duration: number | null;
  awardType: number;
  isSecret: boolean | null;
}
