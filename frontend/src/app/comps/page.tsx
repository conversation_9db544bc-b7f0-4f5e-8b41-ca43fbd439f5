import React from 'react';
import { redirect } from 'next/navigation';
import AccordionExample from './accordionExample';
import BadgesExample from './badges';
import Buttons from './buttons';
import Checkboxes from './checkboxes';
import DialogExample from './dialogExample';
import Notifications from './notifications';
import ProgressBarExample from './progressBar';
import RadiosExample from './radios';
import Switches from './switches';
import TabExample from './tabs';
import Toasts from './toasts';

const Index = () => {
  // このページは本番環境では表示不可
  if (process.env.NODE_ENV === 'production') {
    redirect('/');
    return null;
  }

  return (
    <div>
      <h1 className="mb-6 text-center text-bold-22">Buttons</h1>
      <div className="m-auto mb-3 w-3/4 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import Button from '@/components/atoms/button/button';`}</code>
          <code>{`import SideButton from '@/components/atoms/button/sidebutton';`}</code>
        </pre>
      </div>
      <Buttons />

      <h1 className="mb-6 text-center text-bold-22">Tab</h1>
      <div className="m-auto mb-3 w-3/4 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import Tab from '@/components/atoms/tab';
import Tabs from '@/components/atoms/tab/tabs';`}</code>
        </pre>
      </div>
      <TabExample />
      <h1 className="mb-6 text-center text-bold-22">Switch</h1>
      <div className="m-auto mb-3 w-3/4 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import { Switch } from '@/components/atoms/switch';`}</code>
        </pre>
      </div>
      <Switches />
      <h1 className="mb-6 text-center text-bold-22">Radio</h1>
      <div className="m-auto mb-3 w-3/4 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import RadioGroup from '@/components/atoms/radioGroup';
import RadioGroupItem from '@/components/atoms/radioGroup/radio-group-item';`}</code>
        </pre>
      </div>
      <RadiosExample />
      <h1 className="mb-6 text-center text-bold-22">Notifications</h1>
      <div className="m-auto mb-3 w-3/4 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import Notification from '@/components/atoms/notification';`}</code>
        </pre>
      </div>
      <Notifications />
      <BadgesExample />
      <Toasts />
      <Checkboxes />
      <DialogExample />
      <AccordionExample />
      <ProgressBarExample />
      <h1 className="mb-6 text-center text-bold-22">Progress Bar</h1>
      <div className="m-auto mb-3 w-3/4 rounded-md bg-slate-100 p-4">
        <pre>
          <code>{`import ProgressBar from '@/components/atoms/progress-bar';`}</code>
        </pre>
        <pre>
          <code>
            {`const progressText = (
<div className={clsx('text-shadow flex items-center justify-center', roboto.className)}>
  <span className="text-bold-14">15</span>
  <span className="text-bold-12">/30</span>
</div>
);
return (
  <div className="w-1/2">
    <ProgressBar progress={50} text={progressText} />
  </div>
);`}
          </code>
        </pre>
      </div>
    </div>
  );
};

export default Index;
