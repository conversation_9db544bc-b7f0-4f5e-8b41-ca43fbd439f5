import { notFound, redirect } from 'next/navigation';
import HeaderLayout from '@/components/layouts/Header';
import MainItemViewerSection from './_components/main-item-viewer-section';
import BenefitsSection from './BenefitsSection';
import NotesSection from './NotesSection';
import { getItem } from '@/app/actions/shopItem';
import { getUserIdentityId } from '@/utils/base';
import { ITEM_TYPE } from '@/types/item';
import { ShopItemDetail } from '@/types/shopItem';

type ItemViewerProps = {
  identityId: string;
  itemId: string;
};

const ItemViewer = async ({ params }: { params: ItemViewerProps }) => {
  const identityId = getUserIdentityId(params.identityId);
  if (isNaN(Number(params.itemId))) {
    return notFound();
  }

  let item: ShopItemDetail['item'];

  try {
    const response = await getItem(params.itemId, identityId, true);
    const { data } = await response.json();
    const itemData = data as ShopItemDetail;
    item = itemData.item;
  } catch (e) {
    console.error(e);
    return notFound();
  }

  if (!item) return notFound();

  // 暫定対応 購入済みコンテンツ関連の実装で再修正する
  if (item.itemType === ITEM_TYPE.CHEKI.value || item.itemType === ITEM_TYPE.DIGITAL_GACHA.value) {
    redirect(`/${identityId}/item/${params.itemId}`);
    return null;
  }

  const hasPurchasedFile = item.itemFiles.some((file) => file.isPurchased === true);
  if (!item.isPurchased && !hasPurchasedFile) {
    redirect(`/${identityId}/item/${params.itemId}`);
    return null;
  }

  return (
    <div className="flex flex-col items-center justify-center bg-gray-100">
      <HeaderLayout title={item.title} />
      <MainItemViewerSection itemId={item.id} itemFiles={item.itemFiles} purchasedOnly={true} />
      {item.benefits && item.benefits.benefitFiles.length > 0 && item.isPurchased && (
        <BenefitsSection itemId={item.id} benefits={item.benefits} />
      )}
      <NotesSection />
    </div>
  );
};

export default ItemViewer;
