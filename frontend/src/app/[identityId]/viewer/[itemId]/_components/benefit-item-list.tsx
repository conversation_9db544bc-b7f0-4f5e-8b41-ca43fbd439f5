'use client';

import React from 'react';
import { usePlayerStore, PlayerType } from '@/store/usePlayer';
import BenefitItem from './benefit-item';
import BenefitItemWithTitle from './benefit-item-with-title';
import type { GachaBenefit, GachaBenefitFile } from '@/types/gacha';
import type { SingleItem } from '@/types/shopItem';

const conditionTitleMap: { [key: number]: string } = {
  1: '10回購入特典',
  2: '20回購入特典',
  3: '30回購入特典',
  4: '40回購入特典',
  5: '50回購入特典',
};
interface BenefitItemListProps<T> {
  items: T[];
  readOnly?: boolean;
  onDownload?: (file: SingleItem | GachaBenefitFile) => void;
}

const BenefitItemList = <T extends SingleItem | GachaBenefit['benefit'][number]>({
  items,
  readOnly = true,
  onDownload,
}: BenefitItemListProps<T>) => {
  const { setPlayerProps, onPlayerOpen } = usePlayerStore();

  const handleClick = (id?: string) => {
    if (readOnly) return;
    if (!id) return;

    // SingleItemを検索
    const item = items.find((item: T) => 'id' in item && item.id === id);

    if (item && 'src' in item && 'thumbnail' in item && 'type' in item) {
      onPlayerOpen();
      setPlayerProps({
        src: item.src!,
        thumbnail: item.thumbnail!,
        type: item.type as PlayerType,
      });
    }
  };
  const benefitTitle = (index: number, conditionTitle: string) => {
    return (
      <div className="flex items-center justify-center gap-2">
        <span className="flex h-5 w-12.5 items-center justify-center rounded-m bg-gray-800 text-medium-11 text-white">
          特典 {index + 1}
        </span>
        <span className="text-medium-12 text-secondary">{conditionTitle}</span>
      </div>
    );
  };
  return (
    <div className="flex flex-col items-center gap-0.5">
      {items.map((item: SingleItem | GachaBenefit['benefit'][number], index: number) => {
        if ('benefitFile' in item) {
          const conditionTitle = conditionTitleMap[item.conditionType] || '未設定';

          return (
            <BenefitItemWithTitle
              title={benefitTitle(index, conditionTitle)}
              readOnly={readOnly}
              onDownload={onDownload}
              handleClick={handleClick}
              index={index}
              key={index}
              item={item}
              length={items.length}
            />
          );
        } else {
          return (
            <BenefitItem
              key={item?.id}
              item={item}
              readOnly={readOnly}
              onDownload={onDownload}
              handleClick={handleClick}
              index={index}
              length={items.length}
            />
          );
        }
      })}
    </div>
  );
};

export default BenefitItemList;
