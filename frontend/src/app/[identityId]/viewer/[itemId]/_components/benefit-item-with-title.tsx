import BenefitContent from './benefit-content';
import type { GachaBenefitFile, GachaBenefitItem } from '@/types/gacha';

type BenefitItemProps = {
  title: React.ReactNode;
  length: number;
  item: GachaBenefitItem;
  readOnly?: boolean;
  onDownload?: (file: GachaBenefitFile) => void;
  handleClick: (id?: string) => void;
  index: number;
};

const BenefitItemWithTitle = ({ title, length, readOnly, onDownload, handleClick, index, item }: BenefitItemProps) => {
  return (
    <div
      key={item.benefitFile.id}
      className={`w-full overflow-hidden rounded-m ${index === length - 1 ? '' : 'mb-2'}`}
      onClick={() => !readOnly && handleClick(item.benefitFile.id)}
    >
      <div className="flex h-8 w-full items-center justify-start bg-white pl-4">{title}</div>
      <div className="flex h-10 items-center justify-start gap-1 bg-gray-700 px-4">
        <BenefitContent
          item={item.benefitFile}
          onDownload={onDownload}
          isShowDownload={!readOnly && !!onDownload}
          type={item.benefitFile.type}
          title={item.benefitFile.title}
        />
      </div>
    </div>
  );
};

export default BenefitItemWithTitle;
