'use client';
import { useEffect, useRef, useState } from 'react';
import { sendGTMEvent } from '@next/third-parties/google';
import clsx from 'clsx';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import OutlinedButton from '@/components/atoms/button/outlined-button';
import SideButton from '@/components/atoms/button/side-button';
import NotificationWithNum from '@/components/atoms/notification/notification-with-num';
import ShareIcon from '@/components/atoms/shareIcon';
import CreatorInfo from '@/components/containers/creator-info';
import MenuItemCard from '@/components/containers/MenuItemCard/MenuItemCard';
import ReadMoreButton from '@/components/containers/ReadMoreButton';
import FixedBar from '@/components/layouts/FixedBar';
import ShopPublicImage from '@/components/ShopImage';
import { useGetShop } from '@/lib/client-api/shop-endpoint/shop-endpoint';
import { ShopData } from '@/lib/server-api/shop-api.schemas';
import { useCurrentUser } from '@/store/useCurrentUser';
import { useFullModalStore } from '@/store/useFullModal';
import { useIsEdit } from '@/store/useIsEdit';
import { useShopItemsStore } from '@/store/useItems';
import { useSorting } from '@/store/useSorting';
import ItemsSection from './ItemsSection';
import ItemSkeleton from './TopPageSkelton';
import EditItemsSection from '@/app/[identityId]/EditItemsSection';
import { generateShopUrl } from '@/consts/url';
import { useGetCart } from '@/hooks/swr/useGetCart';
import { useIsFixedAtBottom } from '@/hooks/useIsFixedAtBottom';
import { getIsNowForSale } from '@/utils/item';
import { navigateShare } from '@/utils/share';
import { ShopItemType } from '@/types/shopItem';

type ClientSideShopTopProps = {
  shopData: ShopData;
  shopItems: ShopItemType[];
  identityId: string;
};

const ClientSideShopTop = ({ shopData, shopItems, identityId }: ClientSideShopTopProps) => {
  const router = useRouter();
  const midDiv = useRef<HTMLDivElement>(null);
  const isFixed = useIsFixedAtBottom(midDiv);
  const { currentUser, isOwner, setIsOwner } = useCurrentUser();
  const { setIsEdit, isEdit } = useIsEdit();
  const { sorting } = useSorting();
  const { itemList, setItemList } = useShopItemsStore();
  const [isAvailableItems, setIsAvailableItems] = useState<boolean>();
  const { data: shopResponseBody, isLoading } = useGetShop(identityId);
  const shopLimitation = shopResponseBody?.data?.shop.limitation;
  const { data: cartData } = useGetCart(identityId, identityId);

  const {
    headerImageUri: coverImage,
    description,
    name: shopName,
    creatorName,
    creatorIconUri: creatorIcon,
  } = shopData.shop;

  const { onFullModalOpen, setFullModalProps, onFullModalClose } = useFullModalStore();

  const handleShare = async () => {
    sendGTMEvent({ event: 'fanme_item_list_share', item_id: identityId });
    const url = generateShopUrl(identityId);
    const text = `${creatorName} - ${shopName}\n\n👇👇購入はこちらから！\n${url}\n\n#FANME #ファンミー #FANMEshop\n@FANME__officialより`;
    await navigateShare({ text });
  };

  useEffect(() => {
    const cartItemIds = cartData?.data.items.map((item) => Number(item.itemId)) || [];
    const updatedItems = shopItems.map((item) => ({
      ...item,
      isAdded: cartItemIds.includes(item.id),
    }));
    setItemList(updatedItems);
  }, [shopItems, cartData, setItemList]);

  const onClickShopEditButton = () => {
    router.push(`/@${identityId}/edit?t=${Date.now()}`);
  };

  const searchParams = useSearchParams();
  useEffect(() => {
    if (searchParams.has('editMode')) {
      setIsEdit(true);
    } else {
      setIsEdit(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  useEffect(() => {
    if (currentUser) {
      setIsOwner(identityId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUser]);

  useEffect(() => {
    if (itemList && itemList.length > 0) {
      itemList.forEach((item) => {
        if (isAvailableItems) return;
        if (item.available && getIsNowForSale(item.forSale)) {
          setIsAvailableItems(true);
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemList]);

  // 出品メニューのモダルを出す
  const openItemMenuModal = () => {
    onFullModalOpen();
    setFullModalProps({
      title: '出品メニュー',
      isHeaderWhite: true,
      children: (
        <MenuItemCard isChekiExhibitable={shopLimitation?.isChekiExhibitable ?? false} identityId={identityId} />
      ),
      onClose: onFullModalClose,
    });
  };

  return (
    <>
      <div
        ref={midDiv}
        className={clsx('relative flex flex-1 flex-col', {
          ['pb-30']: !isFixed && isEdit,
          ['pb-2']: !isEdit,
        })}
      >
        <div className="fixed bottom-24 right-[calc((100vw-480px)/2)] z-10 max-PC:right-0">
          {(!isOwner || (isOwner && !isEdit)) && (
            <>
              {!!cartData?.data.totalQuantity && (
                <NotificationWithNum num={cartData?.data.totalQuantity} className="absolute right-0 top-0 z-10" />
              )}
              <SideButton
                sideButtonType="dark"
                buttonDirection="left"
                buttonSize="lg"
                onClick={() => router.push(`/@${identityId}/cart`)}
              >
                <ShopPublicImage src="/images/icons/Cart.svg" width={28} height={28} alt="cart" />
              </SideButton>
            </>
          )}
        </div>
        {!!coverImage && (
          <div className="w-full">
            <Image src={coverImage} width={480} height={160} priority className="w-full object-fill" alt="shop cover" />
          </div>
        )}
        <div className="rounded-b-2xl bg-white p-3">
          <div className="mb-4 flex items-center justify-between px-1">
            <CreatorInfo creatorName={creatorName} identityId={identityId} creatorIcon={creatorIcon} />
            <button className="p-2" onClick={handleShare}>
              <ShareIcon />
            </button>
          </div>
          <div
            className={clsx('flex items-center justify-between', {
              ['mb-3']: !!description,
            })}
          >
            <h1 className="text-bold-15">{shopName}</h1>
            {isOwner && isEdit && !sorting && (
              <OutlinedButton
                buttonColor="black"
                buttonShape="pill"
                buttonType="edit"
                onClick={onClickShopEditButton}
              />
            )}
          </div>
          {description && <ReadMoreButton description={description} />}
        </div>
        {(!isOwner || (isOwner && !isEdit)) &&
          (!itemList ? (
            <ItemSkeleton />
          ) : itemList.length === 0 || !isAvailableItems ? (
            <div className="flex flex-1 items-center justify-center text-center">
              <div className="flex w-full flex-col items-center gap-4">
                <ShopPublicImage src="/images/icons/shopIcon.svg" width={53.33} height={64} alt="empty" />
                <p className="text-bold-18 text-gray-500">商品がまだ登録されていません</p>
                <p className="text-medium-14 text-gray-500">しばらく経ってからまたお越しください</p>
              </div>
            </div>
          ) : itemList?.length > 0 ? (
            <ItemsSection identityId={identityId} items={itemList} />
          ) : (
            <></>
          ))}
        <div className="flex-1">
          {isOwner &&
            isEdit &&
            (!!itemList && itemList.length > 0 ? (
              <EditItemsSection identityId={identityId} />
            ) : (
              <div className="mb-4 mt-8 flex flex-col items-center justify-center">
                <h2 className="text-bold-20 text-orange-200">商品を出品してみましょう</h2>
                <div className="w-full pt-8">
                  <div className="relative aspect-[360/256] w-full">
                    <ShopPublicImage
                      src="/images/registerProduct.webp"
                      alt="register product"
                      fill
                      className="object-contain"
                    />
                  </div>
                </div>
              </div>
            ))}
        </div>
        {isOwner && isEdit && !sorting && (
          <FixedBar isFixed={isFixed}>
            <Button buttonType="dark" disabled={isLoading} onClick={openItemMenuModal}>
              販売方法を選択
            </Button>
          </FixedBar>
        )}
      </div>
    </>
  );
};

export default ClientSideShopTop;
